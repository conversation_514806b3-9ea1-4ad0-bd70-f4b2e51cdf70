using Ardalis.Specification;
using Domain.Entities;

namespace Application.RoleActions.Specifications;

/// <summary>
/// Specification to get RoleActions by RoleId
/// </summary>
public class RoleActionsByRoleIdSpec : Specification<Domain.Entities.RoleActions>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public RoleActionsByRoleIdSpec(Guid roleId, bool includeInactive = false)
    {
        Query.Where(ra => ra.RoleId == roleId && !ra.IsDeleted);

        if (!includeInactive)
        {
            Query.Where(ra => ra.IsActive);
        }

        // Include related entities
        Query.Include(ra => ra.Role);
        Query.Include(ra => ra.Action);

        // Order by creation date
        Query.OrderBy(ra => ra.CreatedAt);
    }
}

/// <summary>
/// Specification to get RoleActions by multiple RoleIds
/// </summary>
public class RoleActionsByRoleIdsSpec : Specification<Domain.Entities.RoleActions>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public RoleActionsByRoleIdsSpec(List<Guid> roleIds, bool includeInactive = false)
    {
        Query.Where(ra => roleIds.Contains(ra.RoleId) && !ra.IsDeleted);

        if (!includeInactive)
        {
            Query.Where(ra => ra.IsActive);
        }

        // Include related entities
        Query.Include(ra => ra.Role);
        Query.Include(ra => ra.Action);

        // Order by role id, then creation date
        Query.OrderBy(ra => ra.RoleId).ThenBy(ra => ra.CreatedAt);
    }
}

/// <summary>
/// Specification to get RoleActions by RoleId and ActionIds
/// </summary>
public class RoleActionsByRoleIdAndActionIdsSpec : Specification<Domain.Entities.RoleActions>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public RoleActionsByRoleIdAndActionIdsSpec(Guid roleId, List<Guid> actionIds)
    {
        Query.Where(ra => ra.RoleId == roleId && 
                         actionIds.Contains(ra.ActionId) && 
                         !ra.IsDeleted);

        // Include related entities
        Query.Include(ra => ra.Role);
        Query.Include(ra => ra.Action);
    }
}

/// <summary>
/// Specification to check if RoleAction exists
/// </summary>
public class RoleActionExistsSpec : Specification<Domain.Entities.RoleActions>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public RoleActionExistsSpec(Guid roleId, Guid actionId)
    {
        Query.Where(ra => ra.RoleId == roleId && 
                         ra.ActionId == actionId && 
                         !ra.IsDeleted);

        // Take only one record for existence check
        Query.Take(1);
    }
}

/// <summary>
/// Specification to get Roles by IDs
/// </summary>
public class RolesByIdsSpec : Specification<Role>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public RolesByIdsSpec(List<Guid> roleIds)
    {
        Query.Where(r => roleIds.Contains(r.Id) && !r.IsDeleted);
    }
}

/// <summary>
/// Specification to get Actions by IDs
/// </summary>
public class ActionsByIdsSpec : Specification<Domain.Entities.Action>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ActionsByIdsSpec(List<Guid> actionIds)
    {
        Query.Where(a => actionIds.Contains(a.Id) && !a.IsDeleted);
    }
}
