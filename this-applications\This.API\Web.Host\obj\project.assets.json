{"version": 3, "targets": {"net8.0": {"Amazon.Extensions.CognitoAuthentication/2.5.5": {"type": "package", "dependencies": {"AWSSDK.CognitoIdentity": "3.7.300.74", "AWSSDK.CognitoIdentityProvider": "3.7.303.19"}, "compile": {"lib/netstandard2.0/Amazon.Extensions.CognitoAuthentication.dll": {}}, "runtime": {"lib/netstandard2.0/Amazon.Extensions.CognitoAuthentication.dll": {}}}, "Ardalis.Specification/8.0.0": {"type": "package", "compile": {"lib/net8.0/Ardalis.Specification.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Ardalis.Specification.dll": {"related": ".pdb;.xml"}}}, "Ardalis.Specification.EntityFrameworkCore/8.0.0": {"type": "package", "dependencies": {"Ardalis.Specification": "8.0.0", "Microsoft.EntityFrameworkCore": "8.0.0", "Microsoft.EntityFrameworkCore.Relational": "8.0.0"}, "compile": {"lib/net8.0/Ardalis.Specification.EntityFrameworkCore.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Ardalis.Specification.EntityFrameworkCore.dll": {"related": ".pdb;.xml"}}}, "Asp.Versioning.Abstractions/8.0.0": {"type": "package", "compile": {"lib/net8.0/Asp.Versioning.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Asp.Versioning.Abstractions.dll": {"related": ".xml"}}}, "Asp.Versioning.Http/8.0.0": {"type": "package", "dependencies": {"Asp.Versioning.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Asp.Versioning.Http.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Asp.Versioning.Http.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Asp.Versioning.Mvc/8.0.0": {"type": "package", "dependencies": {"Asp.Versioning.Http": "8.0.0"}, "compile": {"lib/net8.0/Asp.Versioning.Mvc.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Asp.Versioning.Mvc.dll": {"related": ".xml"}}}, "AWS.Logger.Core/3.1.0": {"type": "package", "dependencies": {"AWSSDK.CloudWatchLogs": "3.7.0.5"}, "compile": {"lib/netstandard2.0/AWS.Logger.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/AWS.Logger.Core.dll": {"related": ".pdb;.xml"}}}, "AWS.Logger.SeriLog/3.2.0": {"type": "package", "dependencies": {"AWS.Logger.Core": "3.1.0", "Serilog": "2.6.0"}, "compile": {"lib/netstandard2.0/AWS.Logger.SeriLog.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/AWS.Logger.SeriLog.dll": {"related": ".pdb;.xml"}}}, "AWSSDK.CloudWatchLogs/3.7.0.5": {"type": "package", "dependencies": {"AWSSDK.Core": "[3.7.0.6, 3.8.0)"}, "compile": {"lib/netcoreapp3.1/AWSSDK.CloudWatchLogs.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/AWSSDK.CloudWatchLogs.dll": {"related": ".pdb;.xml"}}}, "AWSSDK.CognitoIdentity/3.7.300.74": {"type": "package", "dependencies": {"AWSSDK.Core": "[3.7.303.14, 4.0.0)", "AWSSDK.SecurityToken": "[3.7.300.75, 4.0.0)"}, "compile": {"lib/net8.0/AWSSDK.CognitoIdentity.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/AWSSDK.CognitoIdentity.dll": {"related": ".pdb;.xml"}}}, "AWSSDK.CognitoIdentityProvider/3.7.303.19": {"type": "package", "dependencies": {"AWSSDK.Core": "[3.7.303.14, 4.0.0)"}, "compile": {"lib/net8.0/AWSSDK.CognitoIdentityProvider.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/AWSSDK.CognitoIdentityProvider.dll": {"related": ".pdb;.xml"}}}, "AWSSDK.Core/3.7.400.40": {"type": "package", "compile": {"lib/net8.0/AWSSDK.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/AWSSDK.Core.dll": {"related": ".pdb;.xml"}}}, "AWSSDK.S3/3.7.405.4": {"type": "package", "dependencies": {"AWSSDK.Core": "[3.7.400.40, 4.0.0)"}, "compile": {"lib/net8.0/AWSSDK.S3.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/AWSSDK.S3.dll": {"related": ".pdb;.xml"}}}, "AWSSDK.SecurityToken/3.7.300.75": {"type": "package", "dependencies": {"AWSSDK.Core": "[3.7.303.14, 4.0.0)"}, "compile": {"lib/net8.0/AWSSDK.SecurityToken.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/AWSSDK.SecurityToken.dll": {"related": ".pdb;.xml"}}}, "Azure.Core/1.38.0": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.ClientModel": "1.0.0", "System.Memory.Data": "1.0.2"}, "compile": {"lib/net6.0/Azure.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Azure.Core.dll": {"related": ".xml"}}}, "Azure.Identity/1.11.4": {"type": "package", "dependencies": {"Azure.Core": "1.38.0", "Microsoft.Identity.Client": "4.61.3", "Microsoft.Identity.Client.Extensions.Msal": "4.61.3", "System.Security.Cryptography.ProtectedData": "4.7.0"}, "compile": {"lib/netstandard2.0/Azure.Identity.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"related": ".xml"}}}, "Azure.Security.KeyVault.Certificates/4.6.0": {"type": "package", "dependencies": {"Azure.Core": "1.37.0"}, "compile": {"lib/netstandard2.0/Azure.Security.KeyVault.Certificates.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Azure.Security.KeyVault.Certificates.dll": {"related": ".xml"}}}, "Azure.Security.KeyVault.Secrets/4.6.0": {"type": "package", "dependencies": {"Azure.Core": "1.37.0"}, "compile": {"lib/netstandard2.0/Azure.Security.KeyVault.Secrets.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Azure.Security.KeyVault.Secrets.dll": {"related": ".xml"}}}, "Dapper/2.1.35": {"type": "package", "compile": {"lib/net7.0/Dapper.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Dapper.dll": {"related": ".xml"}}}, "DocumentFormat.OpenXml/2.16.0": {"type": "package", "dependencies": {"System.IO.Packaging": "4.7.0"}, "compile": {"lib/netstandard2.0/DocumentFormat.OpenXml.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/DocumentFormat.OpenXml.dll": {"related": ".xml"}}}, "EPPlus/7.6.0": {"type": "package", "dependencies": {"EPPlus.System.Drawing": "7.5.0", "Microsoft.Extensions.Configuration.Json": "9.0.1", "Microsoft.IO.RecyclableMemoryStream": "3.0.1", "System.Security.Cryptography.Pkcs": "9.0.1", "System.Text.Encoding.CodePages": "9.0.1"}, "compile": {"lib/net8.0/EPPlus.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/EPPlus.dll": {"related": ".xml"}}}, "EPPlus.Interfaces/7.5.0": {"type": "package", "compile": {"lib/net8.0/EPPlus.Interfaces.dll": {}}, "runtime": {"lib/net8.0/EPPlus.Interfaces.dll": {}}}, "EPPlus.System.Drawing/7.5.0": {"type": "package", "dependencies": {"EPPlus.Interfaces": "7.5.0", "System.Drawing.Common": "8.0.4"}, "compile": {"lib/net8.0/EPPlus.System.Drawing.dll": {}}, "runtime": {"lib/net8.0/EPPlus.System.Drawing.dll": {}}}, "Finbuckle.MultiTenant/6.13.1": {"type": "package", "compile": {"lib/net8.0/Finbuckle.MultiTenant.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Finbuckle.MultiTenant.dll": {"related": ".xml"}}}, "Finbuckle.MultiTenant.AspNetCore/6.13.1": {"type": "package", "dependencies": {"Finbuckle.MultiTenant": "6.13.1", "Microsoft.AspNetCore.Authentication.OpenIdConnect": "8.0.1"}, "compile": {"lib/net8.0/Finbuckle.MultiTenant.AspNetCore.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Finbuckle.MultiTenant.AspNetCore.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Finbuckle.MultiTenant.EntityFrameworkCore/6.13.1": {"type": "package", "dependencies": {"Finbuckle.MultiTenant": "6.13.1", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "8.0.0", "Microsoft.EntityFrameworkCore.Relational": "8.0.0"}, "compile": {"lib/net8.0/Finbuckle.MultiTenant.EntityFrameworkCore.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Finbuckle.MultiTenant.EntityFrameworkCore.dll": {"related": ".xml"}}}, "FluentValidation/11.9.0": {"type": "package", "compile": {"lib/net8.0/FluentValidation.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/FluentValidation.dll": {"related": ".xml"}}}, "FluentValidation.DependencyInjectionExtensions/11.9.0": {"type": "package", "dependencies": {"FluentValidation": "11.9.0"}, "compile": {"lib/netstandard2.1/FluentValidation.DependencyInjectionExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/FluentValidation.DependencyInjectionExtensions.dll": {"related": ".xml"}}}, "Hangfire/1.8.15": {"type": "package", "dependencies": {"Hangfire.AspNetCore": "[1.8.15]", "Hangfire.Core": "[1.8.15]", "Hangfire.SqlServer": "[1.8.15]"}}, "Hangfire.AspNetCore/1.8.15": {"type": "package", "dependencies": {"Hangfire.NetCore": "[1.8.15]"}, "compile": {"lib/netcoreapp3.0/Hangfire.AspNetCore.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Hangfire.AspNetCore.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Hangfire.Console/1.4.2": {"type": "package", "dependencies": {"Hangfire.Core": "1.6.0", "NETStandard.Library": "1.6.1"}, "compile": {"lib/netstandard1.3/Hangfire.Console.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/Hangfire.Console.dll": {"related": ".xml"}}}, "Hangfire.Console.Extensions/1.0.5": {"type": "package", "dependencies": {"Hangfire.Console": "1.4.2"}, "compile": {"lib/netstandard2.0/Hangfire.Console.Extensions.dll": {}}, "runtime": {"lib/netstandard2.0/Hangfire.Console.Extensions.dll": {}}}, "Hangfire.Core/1.8.15": {"type": "package", "dependencies": {"Newtonsoft.Json": "11.0.1"}, "compile": {"lib/netstandard2.0/Hangfire.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Hangfire.Core.dll": {"related": ".xml"}}, "resource": {"lib/netstandard2.0/ca/Hangfire.Core.resources.dll": {"locale": "ca"}, "lib/netstandard2.0/de/Hangfire.Core.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/Hangfire.Core.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fa/Hangfire.Core.resources.dll": {"locale": "fa"}, "lib/netstandard2.0/fr/Hangfire.Core.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/nb/Hangfire.Core.resources.dll": {"locale": "nb"}, "lib/netstandard2.0/nl/Hangfire.Core.resources.dll": {"locale": "nl"}, "lib/netstandard2.0/pt-BR/Hangfire.Core.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/pt-PT/Hangfire.Core.resources.dll": {"locale": "pt-PT"}, "lib/netstandard2.0/pt/Hangfire.Core.resources.dll": {"locale": "pt"}, "lib/netstandard2.0/sv/Hangfire.Core.resources.dll": {"locale": "sv"}, "lib/netstandard2.0/tr-TR/Hangfire.Core.resources.dll": {"locale": "tr-TR"}, "lib/netstandard2.0/zh-TW/Hangfire.Core.resources.dll": {"locale": "zh-TW"}, "lib/netstandard2.0/zh/Hangfire.Core.resources.dll": {"locale": "zh"}}}, "Hangfire.Dashboard.Basic.Authentication/5.0.0": {"type": "package", "dependencies": {"Hangfire.AspNetCore": "1.7.19", "Hangfire.Core": "1.7.19"}, "compile": {"lib/net5.0/HangfireBasicAuthenticationFilter.dll": {}}, "runtime": {"lib/net5.0/HangfireBasicAuthenticationFilter.dll": {}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Hangfire.NetCore/1.8.15": {"type": "package", "dependencies": {"Hangfire.Core": "[1.8.15]"}, "compile": {"lib/netstandard2.1/Hangfire.NetCore.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Hangfire.NetCore.dll": {"related": ".xml"}}}, "Hangfire.PostgreSql/1.20.10": {"type": "package", "dependencies": {"Dapper": "2.0.123", "Hangfire.Core": "1.8.0", "Npgsql": "6.0.11"}, "compile": {"lib/netstandard2.0/Hangfire.PostgreSql.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Hangfire.PostgreSql.dll": {"related": ".xml"}}}, "Hangfire.SqlServer/1.8.15": {"type": "package", "dependencies": {"Hangfire.Core": "[1.8.15]"}, "compile": {"lib/netstandard2.0/Hangfire.SqlServer.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Hangfire.SqlServer.dll": {"related": ".xml"}}}, "Humanizer.Core/2.14.1": {"type": "package", "compile": {"lib/net6.0/_._": {"related": ".xml"}}, "runtime": {"lib/net6.0/Humanizer.dll": {"related": ".xml"}}}, "Mapster/7.4.0": {"type": "package", "dependencies": {"Mapster.Core": "1.2.1"}, "compile": {"lib/net7.0/Mapster.dll": {}}, "runtime": {"lib/net7.0/Mapster.dll": {}}}, "Mapster.Core/1.2.1": {"type": "package", "compile": {"lib/net7.0/Mapster.Core.dll": {}}, "runtime": {"lib/net7.0/Mapster.Core.dll": {}}}, "Mapster.DependencyInjection/1.0.1": {"type": "package", "dependencies": {"Mapster": "7.4.0"}, "compile": {"lib/net7.0/Mapster.DependencyInjection.dll": {}}, "runtime": {"lib/net7.0/Mapster.DependencyInjection.dll": {}}}, "MediatR/12.2.0": {"type": "package", "dependencies": {"MediatR.Contracts": "[2.0.1, 3.0.0)"}, "compile": {"lib/net6.0/MediatR.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/MediatR.dll": {"related": ".xml"}}}, "MediatR.Contracts/2.0.1": {"type": "package", "compile": {"lib/netstandard2.0/MediatR.Contracts.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/MediatR.Contracts.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.10": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.1.2"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Microsoft.AspNetCore.Authentication.OpenIdConnect/8.0.1": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.1.2"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Authentication.OpenIdConnect.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authentication.OpenIdConnect.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Microsoft.AspNetCore.Connections.Abstractions/8.0.10": {"type": "package", "dependencies": {"Microsoft.Extensions.Features": "8.0.10"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Connections.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Connections.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Cryptography.Internal/8.0.10": {"type": "package", "compile": {"lib/net8.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/8.0.10": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "8.0.10"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.DataProtection/8.0.1": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "8.0.1", "Microsoft.AspNetCore.DataProtection.Abstractions": "8.0.1", "Microsoft.Extensions.Options": "8.0.1"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.DataProtection.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.DataProtection.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.DataProtection.Abstractions/8.0.1": {"type": "package", "compile": {"lib/net8.0/Microsoft.AspNetCore.DataProtection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.DataProtection.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/8.0.10": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Relational": "8.0.10", "Microsoft.Extensions.Identity.Stores": "8.0.10"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Identity.EntityFrameworkCore.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Identity.EntityFrameworkCore.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Mvc.Versioning/5.1.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.AspNetCore.Mvc.Versioning.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Mvc.Versioning.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Microsoft.AspNetCore.OpenApi/8.0.11": {"type": "package", "dependencies": {"Microsoft.OpenApi": "1.4.3"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.OpenApi.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.OpenApi.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Microsoft.AspNetCore.SignalR.Client.Core/8.0.10": {"type": "package", "dependencies": {"Microsoft.AspNetCore.SignalR.Common": "8.0.10", "Microsoft.AspNetCore.SignalR.Protocols.Json": "8.0.10", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.SignalR.Client.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.SignalR.Client.Core.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.SignalR.Common/8.0.10": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "8.0.10", "Microsoft.Extensions.Options": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.SignalR.Common.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.SignalR.Common.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.SignalR.Protocols.Json/8.0.10": {"type": "package", "dependencies": {"Microsoft.AspNetCore.SignalR.Common": "8.0.10"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.SignalR.Protocols.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.SignalR.Protocols.Json.dll": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"type": "package", "compile": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.Bcl.Cryptography/9.0.1": {"type": "package", "dependencies": {"System.Formats.Asn1": "9.0.1"}, "compile": {"lib/net8.0/Microsoft.Bcl.Cryptography.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Bcl.Cryptography.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Bcl.TimeProvider/8.0.1": {"type": "package", "compile": {"lib/net8.0/Microsoft.Bcl.TimeProvider.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Bcl.TimeProvider.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Build.Framework/17.8.3": {"type": "package", "compile": {"ref/net8.0/_._": {"related": ".xml"}}, "runtime": {"lib/net8.0/_._": {"related": ".pdb;.xml"}}}, "Microsoft.Build.Locator/1.7.8": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/Microsoft.Build.Locator.dll": {}}, "build": {"build/_._": {}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {"type": "package", "build": {"buildTransitive/Microsoft.CodeAnalysis.Analyzers.props": {}, "buildTransitive/Microsoft.CodeAnalysis.Analyzers.targets": {}}}, "Microsoft.CodeAnalysis.Common/4.8.0": {"type": "package", "dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.4"}, "compile": {"lib/net7.0/_._": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/net7.0/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"type": "package", "dependencies": {"Microsoft.CodeAnalysis.Common": "[4.8.0]"}, "compile": {"lib/net7.0/_._": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"type": "package", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "[4.8.0]", "Microsoft.CodeAnalysis.Common": "[4.8.0]", "Microsoft.CodeAnalysis.Workspaces.Common": "[4.8.0]"}, "compile": {"lib/net7.0/_._": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"type": "package", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "7.0.0", "Microsoft.CodeAnalysis.Common": "[4.8.0]", "System.Composition": "7.0.0"}, "compile": {"lib/net7.0/_._": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.MSBuild/4.8.0": {"type": "package", "dependencies": {"Microsoft.Build.Framework": "16.10.0", "Microsoft.CodeAnalysis.Common": "[4.8.0]", "Microsoft.CodeAnalysis.Workspaces.Common": "[4.8.0]"}, "compile": {"lib/net7.0/_._": {"related": ".pdb;.runtimeconfig.json;.xml"}}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll": {"related": ".pdb;.runtimeconfig.json;.xml"}, "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.dll": {"related": ".BuildHost.pdb;.BuildHost.runtimeconfig.json;.BuildHost.xml;.pdb;.xml"}}, "resource": {"lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.EntityFrameworkCore/9.0.5": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.5", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.5", "Microsoft.Extensions.Caching.Memory": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.EntityFrameworkCore.props": {}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.5": {"type": "package", "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.5": {"type": "package"}, "Microsoft.EntityFrameworkCore.Design/9.0.5": {"type": "package", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Build.Framework": "17.8.3", "Microsoft.Build.Locator": "1.7.8", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.MSBuild": "4.8.0", "Microsoft.EntityFrameworkCore.Relational": "9.0.5", "Microsoft.Extensions.Caching.Memory": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.DependencyModel": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "Mono.TextTemplating": "3.0.0", "System.Text.Json": "9.0.5"}, "compile": {"lib/net8.0/_._": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll": {"related": ".xml"}}, "build": {"build/net8.0/Microsoft.EntityFrameworkCore.Design.props": {}}}, "Microsoft.EntityFrameworkCore.Relational/9.0.5": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore": "9.0.5", "Microsoft.Extensions.Caching.Memory": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "build": {"build/Microsoft.Extensions.ApiDescription.Server.props": {}, "build/Microsoft.Extensions.ApiDescription.Server.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.props": {}, "buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.targets": {}}}, "Microsoft.Extensions.Caching.Abstractions/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.5"}, "compile": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Caching.Memory/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "compile": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration/9.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.5"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.1", "Microsoft.Extensions.FileProviders.Physical": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Json/9.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.1", "System.Text.Json": "9.0.1"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5"}, "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.5": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyModel/9.0.5": {"type": "package", "dependencies": {"System.Text.Encodings.Web": "9.0.5", "System.Text.Json": "9.0.5"}, "compile": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Features/8.0.10": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.Features.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Features.dll": {"related": ".xml"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.1"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.1", "Microsoft.Extensions.FileSystemGlobbing": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.1": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Identity.Core/8.0.10": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Cryptography.KeyDerivation": "8.0.10", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Options": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.Extensions.Identity.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Identity.Core.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Identity.Stores/8.0.10": {"type": "package", "dependencies": {"Microsoft.Extensions.Identity.Core": "8.0.10", "Microsoft.Extensions.Logging": "8.0.1"}, "compile": {"lib/net8.0/Microsoft.Extensions.Identity.Stores.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Identity.Stores.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "System.Diagnostics.DiagnosticSource": "9.0.5"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Options/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "compile": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Primitives/9.0.5": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Identity.Abstractions/7.1.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Identity.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Identity.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Identity.Client/4.65.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0"}, "compile": {"lib/net6.0/Microsoft.Identity.Client.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"related": ".xml"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"type": "package", "dependencies": {"Microsoft.Identity.Client": "4.61.3", "System.Security.Cryptography.ProtectedData": "4.5.0"}, "compile": {"lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"related": ".xml"}}}, "Microsoft.Identity.Web/3.2.2": {"type": "package", "dependencies": {"Microsoft.Identity.Web.Certificate": "3.2.2", "Microsoft.Identity.Web.Certificateless": "3.2.2", "Microsoft.Identity.Web.TokenAcquisition": "3.2.2", "Microsoft.Identity.Web.TokenCache": "3.2.2", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.1.2", "Microsoft.IdentityModel.Validators": "8.1.2", "System.IdentityModel.Tokens.Jwt": "8.1.2"}, "compile": {"lib/net8.0/Microsoft.Identity.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Identity.Web.dll": {"related": ".xml"}}}, "Microsoft.Identity.Web.Certificate/3.2.2": {"type": "package", "dependencies": {"Azure.Identity": "1.11.4", "Azure.Security.KeyVault.Certificates": "4.6.0", "Azure.Security.KeyVault.Secrets": "4.6.0", "Microsoft.Identity.Abstractions": "7.1.0", "Microsoft.Identity.Web.Certificateless": "3.2.2", "Microsoft.Identity.Web.Diagnostics": "3.2.2"}, "compile": {"lib/net8.0/Microsoft.Identity.Web.Certificate.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Identity.Web.Certificate.dll": {"related": ".xml"}}}, "Microsoft.Identity.Web.Certificateless/3.2.2": {"type": "package", "dependencies": {"Microsoft.Identity.Client": "4.65.0", "Microsoft.IdentityModel.JsonWebTokens": "8.1.2"}, "compile": {"lib/netstandard2.0/Microsoft.Identity.Web.Certificateless.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Identity.Web.Certificateless.dll": {"related": ".xml"}}}, "Microsoft.Identity.Web.Diagnostics/3.2.2": {"type": "package", "compile": {"lib/net8.0/Microsoft.Identity.Web.Diagnostics.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Identity.Web.Diagnostics.dll": {"related": ".xml"}}}, "Microsoft.Identity.Web.TokenAcquisition/3.2.2": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": "8.0.0", "Microsoft.AspNetCore.Authentication.OpenIdConnect": "8.0.0", "Microsoft.Identity.Abstractions": "7.1.0", "Microsoft.Identity.Web.Certificate": "3.2.2", "Microsoft.Identity.Web.Certificateless": "3.2.2", "Microsoft.Identity.Web.TokenCache": "3.2.2", "Microsoft.IdentityModel.Logging": "8.1.2", "Microsoft.IdentityModel.LoggingExtensions": "8.1.2", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.1.2", "System.IdentityModel.Tokens.Jwt": "8.1.2"}, "compile": {"lib/net8.0/Microsoft.Identity.Web.TokenAcquisition.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Identity.Web.TokenAcquisition.dll": {"related": ".xml"}}}, "Microsoft.Identity.Web.TokenCache/3.2.2": {"type": "package", "dependencies": {"Microsoft.AspNetCore.DataProtection": "8.0.1", "Microsoft.Extensions.Caching.Memory": "8.0.1", "Microsoft.Identity.Client": "4.65.0", "Microsoft.Identity.Web.Diagnostics": "3.2.2", "System.Security.Cryptography.Xml": "8.0.1"}, "compile": {"lib/net8.0/Microsoft.Identity.Web.TokenCache.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Identity.Web.TokenCache.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Abstractions/8.1.2": {"type": "package", "compile": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.1.2": {"type": "package", "dependencies": {"Microsoft.Bcl.TimeProvider": "8.0.1", "Microsoft.IdentityModel.Tokens": "8.1.2"}, "compile": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Logging/8.1.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "8.1.2"}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.LoggingExtensions/8.1.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "8.1.2"}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.LoggingExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.LoggingExtensions.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols/8.1.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "8.1.2"}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.1.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols": "8.1.2", "System.IdentityModel.Tokens.Jwt": "8.1.2"}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Tokens/8.1.2": {"type": "package", "dependencies": {"Microsoft.Bcl.TimeProvider": "8.0.1", "Microsoft.IdentityModel.Logging": "8.1.2"}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Validators/8.1.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols": "8.1.2", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.1.2", "Microsoft.IdentityModel.Tokens": "8.1.2", "System.IdentityModel.Tokens.Jwt": "8.1.2"}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Validators.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Validators.dll": {"related": ".xml"}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.1": {"type": "package", "compile": {"lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.OpenApi/1.6.23": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"related": ".pdb;.xml"}}}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets/1.21.0": {"type": "package", "build": {"build/Microsoft.VisualStudio.Azure.Containers.Tools.Targets.props": {}, "build/Microsoft.VisualStudio.Azure.Containers.Tools.Targets.targets": {}}}, "Microsoft.Win32.SystemEvents/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"assetType": "runtime", "rid": "win"}}}, "Mono.TextTemplating/3.0.0": {"type": "package", "dependencies": {"System.CodeDom": "6.0.0"}, "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/Mono.TextTemplating.dll": {}}, "build": {"buildTransitive/Mono.TextTemplating.targets": {}}}, "Namotion.Reflection/3.1.1": {"type": "package", "compile": {"lib/netstandard2.0/Namotion.Reflection.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Namotion.Reflection.dll": {"related": ".xml"}}}, "NETStandard.Library/1.6.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0"}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "NJsonSchema/11.0.0": {"type": "package", "dependencies": {"NJsonSchema.Annotations": "11.0.0", "Namotion.Reflection": "3.1.1", "Newtonsoft.Json": "13.0.3"}, "compile": {"lib/net6.0/NJsonSchema.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/NJsonSchema.dll": {"related": ".xml"}}}, "NJsonSchema.Annotations/11.0.0": {"type": "package", "compile": {"lib/netstandard2.0/NJsonSchema.Annotations.dll": {}}, "runtime": {"lib/netstandard2.0/NJsonSchema.Annotations.dll": {}}}, "NJsonSchema.NewtonsoftJson/11.0.0": {"type": "package", "dependencies": {"NJsonSchema": "11.0.0", "Newtonsoft.Json": "13.0.3"}, "compile": {"lib/netstandard2.0/NJsonSchema.NewtonsoftJson.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NJsonSchema.NewtonsoftJson.dll": {"related": ".xml"}}}, "NJsonSchema.Yaml/11.0.0": {"type": "package", "dependencies": {"NJsonSchema": "11.0.0", "YamlDotNet": "13.7.1"}, "compile": {"lib/netstandard2.0/NJsonSchema.Yaml.dll": {}}, "runtime": {"lib/netstandard2.0/NJsonSchema.Yaml.dll": {}}}, "Npgsql/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.2"}, "compile": {"lib/net8.0/Npgsql.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Npgsql.dll": {"related": ".xml"}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/9.0.4": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore": "[9.0.1, 10.0.0)", "Microsoft.EntityFrameworkCore.Relational": "[9.0.1, 10.0.0)", "Npgsql": "9.0.3"}, "compile": {"lib/net8.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"related": ".xml"}}}, "NSwag.Annotations/14.0.3": {"type": "package", "compile": {"lib/netstandard2.0/NSwag.Annotations.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NSwag.Annotations.dll": {"related": ".xml"}}}, "NSwag.AspNetCore/14.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.3", "NSwag.Annotations": "14.0.3", "NSwag.Core": "14.0.3", "NSwag.Core.Yaml": "14.0.3", "NSwag.Generation": "14.0.3", "NSwag.Generation.AspNetCore": "14.0.3"}, "compile": {"lib/net8.0/NSwag.AspNetCore.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/NSwag.AspNetCore.dll": {"related": ".xml"}}, "build": {"build/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "NSwag.Core/14.0.3": {"type": "package", "dependencies": {"NJsonSchema": "11.0.0"}, "compile": {"lib/netstandard2.0/NSwag.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NSwag.Core.dll": {"related": ".xml"}}}, "NSwag.Core.Yaml/14.0.3": {"type": "package", "dependencies": {"NJsonSchema.Yaml": "11.0.0", "NSwag.Core": "14.0.3"}, "compile": {"lib/netstandard2.0/NSwag.Core.Yaml.dll": {}}, "runtime": {"lib/netstandard2.0/NSwag.Core.Yaml.dll": {}}}, "NSwag.Generation/14.0.3": {"type": "package", "dependencies": {"NJsonSchema.NewtonsoftJson": "11.0.0", "NSwag.Core": "14.0.3"}, "compile": {"lib/netstandard2.0/NSwag.Generation.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NSwag.Generation.dll": {"related": ".xml"}}}, "NSwag.Generation.AspNetCore/14.0.3": {"type": "package", "dependencies": {"NSwag.Generation": "14.0.3"}, "compile": {"lib/net8.0/NSwag.Generation.AspNetCore.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/NSwag.Generation.AspNetCore.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "RestSharp/112.1.0": {"type": "package", "compile": {"lib/net8.0/RestSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/RestSharp.dll": {"related": ".xml"}}}, "Serilog/3.1.1": {"type": "package", "compile": {"lib/net7.0/Serilog.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Serilog.dll": {"related": ".xml"}}}, "Serilog.AspNetCore/8.0.1": {"type": "package", "dependencies": {"Serilog": "3.1.1", "Serilog.Extensions.Hosting": "8.0.0", "Serilog.Extensions.Logging": "8.0.0", "Serilog.Formatting.Compact": "2.0.0", "Serilog.Settings.Configuration": "8.0.0", "Serilog.Sinks.Console": "5.0.0", "Serilog.Sinks.Debug": "2.0.0", "Serilog.Sinks.File": "5.0.0"}, "compile": {"lib/net8.0/Serilog.AspNetCore.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.AspNetCore.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Serilog.Extensions.Hosting/8.0.0": {"type": "package", "dependencies": {"Serilog": "3.1.1", "Serilog.Extensions.Logging": "8.0.0"}, "compile": {"lib/net8.0/Serilog.Extensions.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Extensions.Hosting.dll": {"related": ".xml"}}}, "Serilog.Extensions.Logging/8.0.0": {"type": "package", "dependencies": {"Serilog": "3.1.1"}, "compile": {"lib/net8.0/Serilog.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Extensions.Logging.dll": {"related": ".xml"}}}, "Serilog.Formatting.Compact/2.0.0": {"type": "package", "dependencies": {"Serilog": "3.1.0"}, "compile": {"lib/net7.0/Serilog.Formatting.Compact.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Serilog.Formatting.Compact.dll": {"related": ".xml"}}}, "Serilog.Settings.Configuration/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyModel": "8.0.0", "Serilog": "3.1.1"}, "compile": {"lib/net8.0/Serilog.Settings.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Settings.Configuration.dll": {"related": ".xml"}}}, "Serilog.Sinks.Console/5.0.0": {"type": "package", "dependencies": {"Serilog": "3.1.0"}, "compile": {"lib/net7.0/Serilog.Sinks.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Serilog.Sinks.Console.dll": {"related": ".xml"}}}, "Serilog.Sinks.Debug/2.0.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/netstandard2.1/Serilog.Sinks.Debug.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.Debug.dll": {"related": ".xml"}}}, "Serilog.Sinks.File/5.0.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/net5.0/Serilog.Sinks.File.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0/Serilog.Sinks.File.dll": {"related": ".pdb;.xml"}}}, "Swashbuckle.AspNetCore/8.1.1": {"type": "package", "dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "8.1.1", "Swashbuckle.AspNetCore.SwaggerGen": "8.1.1", "Swashbuckle.AspNetCore.SwaggerUI": "8.1.1"}, "build": {"build/Swashbuckle.AspNetCore.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Swashbuckle.AspNetCore.props": {}}}, "Swashbuckle.AspNetCore.Annotations/8.1.1": {"type": "package", "dependencies": {"Swashbuckle.AspNetCore.SwaggerGen": "8.1.1"}, "compile": {"lib/net8.0/Swashbuckle.AspNetCore.Annotations.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.Annotations.dll": {"related": ".pdb;.xml"}}}, "Swashbuckle.AspNetCore.Swagger/8.1.1": {"type": "package", "dependencies": {"Microsoft.OpenApi": "1.6.23"}, "compile": {"lib/net8.0/Swashbuckle.AspNetCore.Swagger.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.Swagger.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Swashbuckle.AspNetCore.SwaggerGen/8.1.1": {"type": "package", "dependencies": {"Swashbuckle.AspNetCore.Swagger": "8.1.1"}, "compile": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"related": ".pdb;.xml"}}}, "Swashbuckle.AspNetCore.SwaggerUI/8.1.1": {"type": "package", "compile": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "System.ClientModel/1.0.0": {"type": "package", "dependencies": {"System.Memory.Data": "1.0.2"}, "compile": {"lib/net6.0/System.ClientModel.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"related": ".xml"}}}, "System.CodeDom/6.0.0": {"type": "package", "compile": {"lib/net6.0/_._": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Composition/7.0.0": {"type": "package", "dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Convention": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0", "System.Composition.TypedParts": "7.0.0"}, "compile": {"lib/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Composition.AttributedModel/7.0.0": {"type": "package", "compile": {"lib/net7.0/_._": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Composition.AttributedModel.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Composition.Convention/7.0.0": {"type": "package", "dependencies": {"System.Composition.AttributedModel": "7.0.0"}, "compile": {"lib/net7.0/_._": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Composition.Convention.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Composition.Hosting/7.0.0": {"type": "package", "dependencies": {"System.Composition.Runtime": "7.0.0"}, "compile": {"lib/net7.0/_._": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Composition.Hosting.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Composition.Runtime/7.0.0": {"type": "package", "compile": {"lib/net7.0/_._": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Composition.Runtime.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Composition.TypedParts/7.0.0": {"type": "package", "dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0"}, "compile": {"lib/net7.0/_._": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Composition.TypedParts.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Diagnostics.DiagnosticSource/9.0.5": {"type": "package", "compile": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Drawing.Common/8.0.4": {"type": "package", "dependencies": {"Microsoft.Win32.SystemEvents": "8.0.0"}, "compile": {"lib/net8.0/System.Drawing.Common.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/System.Drawing.Common.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Formats.Asn1/9.0.1": {"type": "package", "compile": {"lib/net8.0/System.Formats.Asn1.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Formats.Asn1.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.IdentityModel.Tokens.Jwt/8.1.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.1.2", "Microsoft.IdentityModel.Tokens": "8.1.2"}, "compile": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}}, "System.IO.Packaging/4.7.0": {"type": "package", "compile": {"ref/netstandard2.0/System.IO.Packaging.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.IO.Packaging.dll": {"related": ".xml"}}}, "System.IO.Pipelines/9.0.5": {"type": "package", "compile": {"lib/net8.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Memory.Data/1.0.2": {"type": "package", "compile": {"lib/netstandard2.0/System.Memory.Data.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"related": ".xml"}}}, "System.Security.Cryptography.Pkcs/9.0.1": {"type": "package", "dependencies": {"Microsoft.Bcl.Cryptography": "9.0.1", "System.Formats.Asn1": "9.0.1"}, "compile": {"lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.ProtectedData/4.7.0": {"type": "package", "compile": {"ref/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Xml/8.0.1": {"type": "package", "compile": {"lib/net8.0/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Text.Encoding.CodePages/9.0.1": {"type": "package", "compile": {"lib/net8.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Text.Encoding.CodePages.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encodings.Web/9.0.5": {"type": "package", "compile": {"lib/net8.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {"assetType": "runtime", "rid": "browser"}}}, "System.Text.Json/9.0.5": {"type": "package", "dependencies": {"System.IO.Pipelines": "9.0.5", "System.Text.Encodings.Web": "9.0.5"}, "compile": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/System.Text.Json.targets": {}}}, "YamlDotNet/13.7.1": {"type": "package", "compile": {"lib/net7.0/YamlDotNet.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/YamlDotNet.dll": {"related": ".xml"}}}, "Abstraction/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"Amazon.Extensions.CognitoAuthentication": "2.5.5", "Ardalis.Specification": "8.0.0", "Dapper": "2.1.35", "Domain": "1.0.0"}, "compile": {"bin/placeholder/Abstraction.dll": {}}, "runtime": {"bin/placeholder/Abstraction.dll": {}}}, "Application/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"AWSSDK.S3": "3.7.405.4", "Abstraction": "1.0.0", "DocumentFormat.OpenXml": "2.16.0", "Domain": "1.0.0", "EPPlus": "7.6.0", "FluentValidation": "11.9.0", "FluentValidation.DependencyInjectionExtensions": "11.9.0", "Hangfire": "1.8.15", "Hangfire.Console": "1.4.2", "Hangfire.Console.Extensions": "1.0.5", "Hangfire.Dashboard.Basic.Authentication": "5.0.0", "Hangfire.PostgreSql": "1.20.10", "Mapster": "7.4.0", "Mapster.DependencyInjection": "1.0.1", "MediatR": "12.2.0", "Microsoft.AspNetCore.SignalR.Client.Core": "8.0.10", "Npgsql.EntityFrameworkCore.PostgreSQL": "8.0.8", "RestSharp": "112.1.0", "Shared": "1.0.0"}, "compile": {"bin/placeholder/Application.dll": {}}, "runtime": {"bin/placeholder/Application.dll": {}}}, "Domain/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"Finbuckle.MultiTenant": "6.13.1", "Finbuckle.MultiTenant.AspNetCore": "6.13.1", "Finbuckle.MultiTenant.EntityFrameworkCore": "6.13.1", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "8.0.10", "Microsoft.Identity.Web": "3.2.2", "Shared": "1.0.0"}, "compile": {"bin/placeholder/Domain.dll": {}}, "runtime": {"bin/placeholder/Domain.dll": {}}}, "Infrastructure/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"AWS.Logger.SeriLog": "3.2.0", "Application": "1.0.0", "Ardalis.Specification.EntityFrameworkCore": "8.0.0", "Asp.Versioning.Mvc": "8.0.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "8.0.10", "Microsoft.AspNetCore.Mvc.Versioning": "5.1.0", "Microsoft.EntityFrameworkCore": "9.0.5", "NSwag.AspNetCore": "14.0.3", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.4", "Serilog.AspNetCore": "8.0.1", "Swashbuckle.AspNetCore": "8.1.1", "Swashbuckle.AspNetCore.Annotations": "8.1.1"}, "compile": {"bin/placeholder/Infrastructure.dll": {}}, "runtime": {"bin/placeholder/Infrastructure.dll": {}}}, "Migrators/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"Infrastructure": "1.0.0", "Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Configuration.Json": "9.0.1"}, "compile": {"bin/placeholder/Migrators.dll": {}}, "runtime": {"bin/placeholder/Migrators.dll": {}}}, "Shared/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"Newtonsoft.Json": "13.0.3"}, "compile": {"bin/placeholder/Shared.dll": {}}, "runtime": {"bin/placeholder/Shared.dll": {}}}}}, "libraries": {"Amazon.Extensions.CognitoAuthentication/2.5.5": {"sha512": "IrXyYiVHsCl58MNVBgzSTArRjhs970Cm/8HE/Kq4r5iPaPhk8V1YJySFZahZPovmc+xCKTQPaeg0XXNuOzJRZg==", "type": "package", "path": "amazon.extensions.cognitoauthentication/2.5.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "amazon.extensions.cognitoauthentication.2.5.5.nupkg.sha512", "amazon.extensions.cognitoauthentication.nuspec", "icon.png", "lib/netstandard2.0/Amazon.Extensions.CognitoAuthentication.dll"]}, "Ardalis.Specification/8.0.0": {"sha512": "1/YP932oZJtaDlm85rWfqvgOjALbk2+VDZaj36sM2TdsJMNkVqMbF5UzY6f7OfzA8eymv3hnCXIgYmEBEiUOhQ==", "type": "package", "path": "ardalis.specification/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "ardalis.specification.8.0.0.nupkg.sha512", "ardalis.specification.nuspec", "icon.png", "lib/net6.0/Ardalis.Specification.dll", "lib/net6.0/Ardalis.Specification.pdb", "lib/net6.0/Ardalis.Specification.xml", "lib/net7.0/Ardalis.Specification.dll", "lib/net7.0/Ardalis.Specification.pdb", "lib/net7.0/Ardalis.Specification.xml", "lib/net8.0/Ardalis.Specification.dll", "lib/net8.0/Ardalis.Specification.pdb", "lib/net8.0/Ardalis.Specification.xml", "lib/netstandard2.0/Ardalis.Specification.dll", "lib/netstandard2.0/Ardalis.Specification.pdb", "lib/netstandard2.0/Ardalis.Specification.xml"]}, "Ardalis.Specification.EntityFrameworkCore/8.0.0": {"sha512": "fDaCf5/7rm7Ul7yE8YafyWhxCSmWCPd+/4BUxM26Hbnb2PKZJssjAevzx9ZceASJKe4HUndSEKXJ8iz+R3rsIw==", "type": "package", "path": "ardalis.specification.entityframeworkcore/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "ardalis.specification.entityframeworkcore.8.0.0.nupkg.sha512", "ardalis.specification.entityframeworkcore.nuspec", "icon.png", "lib/net6.0/Ardalis.Specification.EntityFrameworkCore.dll", "lib/net6.0/Ardalis.Specification.EntityFrameworkCore.pdb", "lib/net6.0/Ardalis.Specification.EntityFrameworkCore.xml", "lib/net7.0/Ardalis.Specification.EntityFrameworkCore.dll", "lib/net7.0/Ardalis.Specification.EntityFrameworkCore.pdb", "lib/net7.0/Ardalis.Specification.EntityFrameworkCore.xml", "lib/net8.0/Ardalis.Specification.EntityFrameworkCore.dll", "lib/net8.0/Ardalis.Specification.EntityFrameworkCore.pdb", "lib/net8.0/Ardalis.Specification.EntityFrameworkCore.xml"]}, "Asp.Versioning.Abstractions/8.0.0": {"sha512": "YkuUcrqi862hw/p8dKCsfOQ6y2mWTfjKHuQoFUA9GOaoBGZsu/FzsoBm6z28WkQIlXCZR1SG7safgHj2WCO/lw==", "type": "package", "path": "asp.versioning.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "asp.versioning.abstractions.8.0.0.nupkg.sha512", "asp.versioning.abstractions.nuspec", "icon.png", "lib/net8.0/Asp.Versioning.Abstractions.dll", "lib/net8.0/Asp.Versioning.Abstractions.xml", "lib/netstandard1.0/Asp.Versioning.Abstractions.dll", "lib/netstandard1.0/Asp.Versioning.Abstractions.xml", "lib/netstandard2.0/Asp.Versioning.Abstractions.dll", "lib/netstandard2.0/Asp.Versioning.Abstractions.xml"]}, "Asp.Versioning.Http/8.0.0": {"sha512": "pxHp26pXoCCIFUxFZz4UWPuYDFJ3PB0QpTpC4QRoLhu90eE3FBs0zmDeRO6Sb3y6hMnk6efdQHGbyCzd7XQIrA==", "type": "package", "path": "asp.versioning.http/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "asp.versioning.http.8.0.0.nupkg.sha512", "asp.versioning.http.nuspec", "icon.png", "lib/net8.0/Asp.Versioning.Http.dll", "lib/net8.0/Asp.Versioning.Http.xml"]}, "Asp.Versioning.Mvc/8.0.0": {"sha512": "zxuA7J6HewdjFF/9Cfb6VWovBTj3MdmLg6PztInratlXGpJ+BZjQzoT8FEMOurzmCxbEFPlQmeMW7b1iJKfsdg==", "type": "package", "path": "asp.versioning.mvc/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "asp.versioning.mvc.8.0.0.nupkg.sha512", "asp.versioning.mvc.nuspec", "icon.png", "lib/net8.0/Asp.Versioning.Mvc.dll", "lib/net8.0/Asp.Versioning.Mvc.xml"]}, "AWS.Logger.Core/3.1.0": {"sha512": "ruKBNASE/IBnVZhyeUy0ueuxq1CuMtDHNpU2cqGUg12SxvClkqqY/PVXyM6I4ltsMh/1tF8WdpTBj9k3Fw/PgA==", "type": "package", "path": "aws.logger.core/3.1.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "aws.logger.core.3.1.0.nupkg.sha512", "aws.logger.core.nuspec", "icon.png", "lib/net45/AWS.Logger.Core.dll", "lib/net45/AWS.Logger.Core.pdb", "lib/net45/AWS.Logger.Core.xml", "lib/netstandard2.0/AWS.Logger.Core.dll", "lib/netstandard2.0/AWS.Logger.Core.pdb", "lib/netstandard2.0/AWS.Logger.Core.xml"]}, "AWS.Logger.SeriLog/3.2.0": {"sha512": "zslsNobQR3j2o+NrlcuVmrWLNyoyeZKIs5n0tO4+Hz6Z2Wcr9FqqgMxzXpKzkYBzQzfYCB/SlovOGvuWEqnnew==", "type": "package", "path": "aws.logger.serilog/3.2.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "aws.logger.serilog.3.2.0.nupkg.sha512", "aws.logger.serilog.nuspec", "icon.png", "lib/net45/AWS.Logger.SeriLog.dll", "lib/net45/AWS.Logger.SeriLog.pdb", "lib/net45/AWS.Logger.SeriLog.xml", "lib/net46/AWS.Logger.SeriLog.dll", "lib/net46/AWS.Logger.SeriLog.pdb", "lib/net46/AWS.Logger.SeriLog.xml", "lib/netstandard2.0/AWS.Logger.SeriLog.dll", "lib/netstandard2.0/AWS.Logger.SeriLog.pdb", "lib/netstandard2.0/AWS.Logger.SeriLog.xml"]}, "AWSSDK.CloudWatchLogs/3.7.0.5": {"sha512": "E9mEMaCVStnkzrs2gb35AbMz8xEGCqMGSnk+yyFJbkmdu9a+w5MVsuwRMkHbqiKn1bs5FTpdQJlV/NtgSYeHbA==", "type": "package", "path": "awssdk.cloudwatchlogs/3.7.0.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/AWSSDK.CloudWatchLogs.CodeAnalysis.dll", "awssdk.cloudwatchlogs.3.7.0.5.nupkg.sha512", "awssdk.cloudwatchlogs.nuspec", "lib/net35/AWSSDK.CloudWatchLogs.dll", "lib/net35/AWSSDK.CloudWatchLogs.pdb", "lib/net35/AWSSDK.CloudWatchLogs.xml", "lib/net45/AWSSDK.CloudWatchLogs.dll", "lib/net45/AWSSDK.CloudWatchLogs.pdb", "lib/net45/AWSSDK.CloudWatchLogs.xml", "lib/netcoreapp3.1/AWSSDK.CloudWatchLogs.dll", "lib/netcoreapp3.1/AWSSDK.CloudWatchLogs.pdb", "lib/netcoreapp3.1/AWSSDK.CloudWatchLogs.xml", "lib/netstandard2.0/AWSSDK.CloudWatchLogs.dll", "lib/netstandard2.0/AWSSDK.CloudWatchLogs.pdb", "lib/netstandard2.0/AWSSDK.CloudWatchLogs.xml", "tools/install.ps1", "tools/uninstall.ps1"]}, "AWSSDK.CognitoIdentity/3.7.300.74": {"sha512": "2WjZrVJn7nm6rWZyDEXVgdyZVdTze4pfZjxdr0Q9gEi+7fCq40z//YQLAlwD44UlzVWpONdLvjcCuA39T5Q/QA==", "type": "package", "path": "awssdk.cognitoidentity/3.7.300.74", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/AWSSDK.CognitoIdentity.CodeAnalysis.dll", "analyzers/dotnet/cs/SharedAnalysisCode.dll", "awssdk.cognitoidentity.3.7.300.74.nupkg.sha512", "awssdk.cognitoidentity.nuspec", "images/AWSLogo.png", "lib/net35/AWSSDK.CognitoIdentity.dll", "lib/net35/AWSSDK.CognitoIdentity.pdb", "lib/net35/AWSSDK.CognitoIdentity.xml", "lib/net45/AWSSDK.CognitoIdentity.dll", "lib/net45/AWSSDK.CognitoIdentity.pdb", "lib/net45/AWSSDK.CognitoIdentity.xml", "lib/net8.0/AWSSDK.CognitoIdentity.dll", "lib/net8.0/AWSSDK.CognitoIdentity.pdb", "lib/net8.0/AWSSDK.CognitoIdentity.xml", "lib/netcoreapp3.1/AWSSDK.CognitoIdentity.dll", "lib/netcoreapp3.1/AWSSDK.CognitoIdentity.pdb", "lib/netcoreapp3.1/AWSSDK.CognitoIdentity.xml", "lib/netstandard2.0/AWSSDK.CognitoIdentity.dll", "lib/netstandard2.0/AWSSDK.CognitoIdentity.pdb", "lib/netstandard2.0/AWSSDK.CognitoIdentity.xml", "tools/install.ps1", "tools/uninstall.ps1"]}, "AWSSDK.CognitoIdentityProvider/3.7.303.19": {"sha512": "sw+JuC+IWlXkcXCOpU+dCiLTghhwat+AJ0sl5CHKaZaR71ZDq5iBsYIN6DgInUHxJuJaf7hhvm+Ot9TfpWTJGA==", "type": "package", "path": "awssdk.cognitoidentityprovider/3.7.303.19", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/AWSSDK.CognitoIdentityProvider.CodeAnalysis.dll", "analyzers/dotnet/cs/SharedAnalysisCode.dll", "awssdk.cognitoidentityprovider.3.7.303.19.nupkg.sha512", "awssdk.cognitoidentityprovider.nuspec", "images/AWSLogo.png", "lib/net35/AWSSDK.CognitoIdentityProvider.dll", "lib/net35/AWSSDK.CognitoIdentityProvider.pdb", "lib/net35/AWSSDK.CognitoIdentityProvider.xml", "lib/net45/AWSSDK.CognitoIdentityProvider.dll", "lib/net45/AWSSDK.CognitoIdentityProvider.pdb", "lib/net45/AWSSDK.CognitoIdentityProvider.xml", "lib/net8.0/AWSSDK.CognitoIdentityProvider.dll", "lib/net8.0/AWSSDK.CognitoIdentityProvider.pdb", "lib/net8.0/AWSSDK.CognitoIdentityProvider.xml", "lib/netcoreapp3.1/AWSSDK.CognitoIdentityProvider.dll", "lib/netcoreapp3.1/AWSSDK.CognitoIdentityProvider.pdb", "lib/netcoreapp3.1/AWSSDK.CognitoIdentityProvider.xml", "lib/netstandard2.0/AWSSDK.CognitoIdentityProvider.dll", "lib/netstandard2.0/AWSSDK.CognitoIdentityProvider.pdb", "lib/netstandard2.0/AWSSDK.CognitoIdentityProvider.xml", "tools/install.ps1", "tools/uninstall.ps1"]}, "AWSSDK.Core/3.7.400.40": {"sha512": "Bt3id437I3k363mHCvBdpUwdw9P2zM3te+wCZ2EWv+PIOyz1NbYqTfrn04U3x333wxsOfT9Ln+cUJeUw96HsTw==", "type": "package", "path": "awssdk.core/3.7.400.40", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "awssdk.core.3.7.400.40.nupkg.sha512", "awssdk.core.nuspec", "images/AWSLogo.png", "lib/net35/AWSSDK.Core.dll", "lib/net35/AWSSDK.Core.pdb", "lib/net35/AWSSDK.Core.xml", "lib/net45/AWSSDK.Core.dll", "lib/net45/AWSSDK.Core.pdb", "lib/net45/AWSSDK.Core.xml", "lib/net8.0/AWSSDK.Core.dll", "lib/net8.0/AWSSDK.Core.pdb", "lib/net8.0/AWSSDK.Core.xml", "lib/netcoreapp3.1/AWSSDK.Core.dll", "lib/netcoreapp3.1/AWSSDK.Core.pdb", "lib/netcoreapp3.1/AWSSDK.Core.xml", "lib/netstandard2.0/AWSSDK.Core.dll", "lib/netstandard2.0/AWSSDK.Core.pdb", "lib/netstandard2.0/AWSSDK.Core.xml", "tools/account-management.ps1"]}, "AWSSDK.S3/3.7.405.4": {"sha512": "H+BTXyHB5nxS2B3jv/Ur350TLWx5kRk3S1+Tldtr90kB6/FgjvOp4eFgjl6L51f9yMyn6GuODtukt1vJfKJR8w==", "type": "package", "path": "awssdk.s3/3.7.405.4", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/AWSSDK.S3.CodeAnalysis.dll", "analyzers/dotnet/cs/SharedAnalysisCode.dll", "awssdk.s3.3.7.405.4.nupkg.sha512", "awssdk.s3.nuspec", "images/AWSLogo.png", "lib/net35/AWSSDK.S3.dll", "lib/net35/AWSSDK.S3.pdb", "lib/net35/AWSSDK.S3.xml", "lib/net45/AWSSDK.S3.dll", "lib/net45/AWSSDK.S3.pdb", "lib/net45/AWSSDK.S3.xml", "lib/net8.0/AWSSDK.S3.dll", "lib/net8.0/AWSSDK.S3.pdb", "lib/net8.0/AWSSDK.S3.xml", "lib/netcoreapp3.1/AWSSDK.S3.dll", "lib/netcoreapp3.1/AWSSDK.S3.pdb", "lib/netcoreapp3.1/AWSSDK.S3.xml", "lib/netstandard2.0/AWSSDK.S3.dll", "lib/netstandard2.0/AWSSDK.S3.pdb", "lib/netstandard2.0/AWSSDK.S3.xml", "tools/install.ps1", "tools/uninstall.ps1"]}, "AWSSDK.SecurityToken/3.7.300.75": {"sha512": "Tr1rbOf9DrZTr3XWPDDnwBjigjKf/QLRIB2V4XGEqaBDYUHnRBuckG2sMNpsq/q9tHSvYvvFXCfIH7R0EpNJBg==", "type": "package", "path": "awssdk.securitytoken/3.7.300.75", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/AWSSDK.SecurityToken.CodeAnalysis.dll", "analyzers/dotnet/cs/SharedAnalysisCode.dll", "awssdk.securitytoken.3.7.300.75.nupkg.sha512", "awssdk.securitytoken.nuspec", "images/AWSLogo.png", "lib/net35/AWSSDK.SecurityToken.dll", "lib/net35/AWSSDK.SecurityToken.pdb", "lib/net35/AWSSDK.SecurityToken.xml", "lib/net45/AWSSDK.SecurityToken.dll", "lib/net45/AWSSDK.SecurityToken.pdb", "lib/net45/AWSSDK.SecurityToken.xml", "lib/net8.0/AWSSDK.SecurityToken.dll", "lib/net8.0/AWSSDK.SecurityToken.pdb", "lib/net8.0/AWSSDK.SecurityToken.xml", "lib/netcoreapp3.1/AWSSDK.SecurityToken.dll", "lib/netcoreapp3.1/AWSSDK.SecurityToken.pdb", "lib/netcoreapp3.1/AWSSDK.SecurityToken.xml", "lib/netstandard2.0/AWSSDK.SecurityToken.dll", "lib/netstandard2.0/AWSSDK.SecurityToken.pdb", "lib/netstandard2.0/AWSSDK.SecurityToken.xml", "tools/install.ps1", "tools/uninstall.ps1"]}, "Azure.Core/1.38.0": {"sha512": "IuEgCoVA0ef7E4pQtpC3+TkPbzaoQfa77HlfJDmfuaJUCVJmn7fT0izamZiryW5sYUFKizsftIxMkXKbgIcPMQ==", "type": "package", "path": "azure.core/1.38.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.core.1.38.0.nupkg.sha512", "azure.core.nuspec", "azureicon.png", "lib/net461/Azure.Core.dll", "lib/net461/Azure.Core.xml", "lib/net472/Azure.Core.dll", "lib/net472/Azure.Core.xml", "lib/net6.0/Azure.Core.dll", "lib/net6.0/Azure.Core.xml", "lib/netstandard2.0/Azure.Core.dll", "lib/netstandard2.0/Azure.Core.xml"]}, "Azure.Identity/1.11.4": {"sha512": "Sf4BoE6Q3jTgFkgBkx7qztYOFELBCo+wQgpYDwal/qJ1unBH73ywPztIJKXBXORRzAeNijsuxhk94h0TIMvfYg==", "type": "package", "path": "azure.identity/1.11.4", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.identity.1.11.4.nupkg.sha512", "azure.identity.nuspec", "azureicon.png", "lib/netstandard2.0/Azure.Identity.dll", "lib/netstandard2.0/Azure.Identity.xml"]}, "Azure.Security.KeyVault.Certificates/4.6.0": {"sha512": "e2ATU/n2ZDL/S8A8EdrcfKEvKc2BojCrrSpmM+JKnrSTQS32x/W0Ldu8utk+epLKwXvSJRSWtlgdo7X8hG1mCg==", "type": "package", "path": "azure.security.keyvault.certificates/4.6.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.security.keyvault.certificates.4.6.0.nupkg.sha512", "azure.security.keyvault.certificates.nuspec", "azureicon.png", "lib/netstandard2.0/Azure.Security.KeyVault.Certificates.dll", "lib/netstandard2.0/Azure.Security.KeyVault.Certificates.xml"]}, "Azure.Security.KeyVault.Secrets/4.6.0": {"sha512": "vwPceoznuT6glvirZcXlaCQrh1uzTSxpZUi2hRFNumHiS3hVyqIXI5fgWiLtlBzwqPJMTr0flUoSvGKjXXQlfg==", "type": "package", "path": "azure.security.keyvault.secrets/4.6.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.security.keyvault.secrets.4.6.0.nupkg.sha512", "azure.security.keyvault.secrets.nuspec", "azureicon.png", "lib/netstandard2.0/Azure.Security.KeyVault.Secrets.dll", "lib/netstandard2.0/Azure.Security.KeyVault.Secrets.xml"]}, "Dapper/2.1.35": {"sha512": "YKRwjVfrG7GYOovlGyQoMvr1/IJdn+7QzNXJxyMh0YfFF5yvDmTYaJOVYWsckreNjGsGSEtrMTpnzxTUq/tZQw==", "type": "package", "path": "dapper/2.1.35", "files": [".nupkg.metadata", ".signature.p7s", "Dapper.png", "dapper.2.1.35.nupkg.sha512", "dapper.nuspec", "lib/net461/Dapper.dll", "lib/net461/Dapper.xml", "lib/net5.0/Dapper.dll", "lib/net5.0/Dapper.xml", "lib/net7.0/Dapper.dll", "lib/net7.0/Dapper.xml", "lib/netstandard2.0/Dapper.dll", "lib/netstandard2.0/Dapper.xml", "readme.md"]}, "DocumentFormat.OpenXml/2.16.0": {"sha512": "RhpnDgyyx1KjZm98T3w3bSXFHG/7ZNUaVmz4NAUA+jmV3PcVNZeW87Y04CpBNLdDHEMSspirfo0B5kLRaoE97w==", "type": "package", "path": "documentformat.openxml/2.16.0", "files": [".nupkg.metadata", ".signature.p7s", "documentformat.openxml.2.16.0.nupkg.sha512", "documentformat.openxml.nuspec", "icon.png", "lib/net35/DocumentFormat.OpenXml.dll", "lib/net35/DocumentFormat.OpenXml.xml", "lib/net40/DocumentFormat.OpenXml.dll", "lib/net40/DocumentFormat.OpenXml.xml", "lib/net46/DocumentFormat.OpenXml.dll", "lib/net46/DocumentFormat.OpenXml.xml", "lib/netstandard1.3/DocumentFormat.OpenXml.dll", "lib/netstandard1.3/DocumentFormat.OpenXml.xml", "lib/netstandard2.0/DocumentFormat.OpenXml.dll", "lib/netstandard2.0/DocumentFormat.OpenXml.xml"]}, "EPPlus/7.6.0": {"sha512": "ve22wl/mlqjDsiewxXk4edACuIATsDZPFVOyg9l2xSnMLqN6nJHJ0q0MaaDDJwVrnfyTdA9DTJ1MF449gXi+Nw==", "type": "package", "path": "epplus/7.6.0", "files": [".nupkg.metadata", ".signature.p7s", "EPPlusLogo.png", "epplus.7.6.0.nupkg.sha512", "epplus.nuspec", "lib/net35/EPPlus.dll", "lib/net35/EPPlus.xml", "lib/net462/EPPlus.dll", "lib/net462/EPPlus.xml", "lib/net8.0/EPPlus.dll", "lib/net8.0/EPPlus.xml", "lib/net9.0/EPPlus.dll", "lib/net9.0/EPPlus.xml", "lib/netstandard2.0/EPPlus.dll", "lib/netstandard2.0/EPPlus.xml", "lib/netstandard2.1/EPPlus.dll", "lib/netstandard2.1/EPPlus.xml", "license.md", "readme.md", "readme.txt"]}, "EPPlus.Interfaces/7.5.0": {"sha512": "mGLKgdIKkXRYIu+HIGmZUngVAAlPzIQgI/KqG10m6P5P2112l6p/5dDa35UHu4GV4Qevw0Pq9PxAymrrrl4tzA==", "type": "package", "path": "epplus.interfaces/7.5.0", "files": [".nupkg.metadata", ".signature.p7s", "EPPlusLogo.png", "epplus.interfaces.7.5.0.nupkg.sha512", "epplus.interfaces.nuspec", "lib/net35/EPPlus.Interfaces.dll", "lib/net462/EPPlus.Interfaces.dll", "lib/net6.0/EPPlus.Interfaces.dll", "lib/net7.0/EPPlus.Interfaces.dll", "lib/net8.0/EPPlus.Interfaces.dll", "lib/netstandard2.0/EPPlus.Interfaces.dll", "lib/netstandard2.1/EPPlus.Interfaces.dll", "license.md", "readme.md"]}, "EPPlus.System.Drawing/7.5.0": {"sha512": "cgwstM12foFisisURUyxwJOWHMD/rZxPSyBXFsCOFayaKq0oKlOs1mCTueKNNIlpPDG1no9vcaQiJgZXFM4KPA==", "type": "package", "path": "epplus.system.drawing/7.5.0", "files": [".nupkg.metadata", ".signature.p7s", "EPPlusLogo.png", "epplus.system.drawing.7.5.0.nupkg.sha512", "epplus.system.drawing.nuspec", "lib/net35/EPPlus.System.Drawing.dll", "lib/net462/EPPlus.System.Drawing.dll", "lib/net6.0/EPPlus.System.Drawing.dll", "lib/net7.0/EPPlus.System.Drawing.dll", "lib/net8.0/EPPlus.System.Drawing.dll", "lib/netstandard2.0/EPPlus.System.Drawing.dll", "lib/netstandard2.1/EPPlus.System.Drawing.dll", "license.md", "readme.md"]}, "Finbuckle.MultiTenant/6.13.1": {"sha512": "KCO5j2Ehl+R9ZoiC5LSS0jUCLhmGKUCmIaBAB8pymUPhFsTVO1jBDCTD8DXvP177yZUm1v/iJ28/uIoyBUesdQ==", "type": "package", "path": "finbuckle.multitenant/6.13.1", "files": [".nupkg.metadata", ".signature.p7s", "finbuckle-128x128.png", "finbuckle.multitenant.6.13.1.nupkg.sha512", "finbuckle.multitenant.nuspec", "lib/net6.0/Finbuckle.MultiTenant.dll", "lib/net6.0/Finbuckle.MultiTenant.xml", "lib/net7.0/Finbuckle.MultiTenant.dll", "lib/net7.0/Finbuckle.MultiTenant.xml", "lib/net8.0/Finbuckle.MultiTenant.dll", "lib/net8.0/Finbuckle.MultiTenant.xml"]}, "Finbuckle.MultiTenant.AspNetCore/6.13.1": {"sha512": "WLJmfHEQOVToAHOYwzbfW/XXGCna0wTaOyTqLvsiFFTnajXakjjgB8ytZYz0EcLScbP/05IFNzoxp/q8LE+XhQ==", "type": "package", "path": "finbuckle.multitenant.aspnetcore/6.13.1", "files": [".nupkg.metadata", ".signature.p7s", "finbuckle-128x128.png", "finbuckle.multitenant.aspnetcore.6.13.1.nupkg.sha512", "finbuckle.multitenant.aspnetcore.nuspec", "lib/net6.0/Finbuckle.MultiTenant.AspNetCore.dll", "lib/net6.0/Finbuckle.MultiTenant.AspNetCore.xml", "lib/net7.0/Finbuckle.MultiTenant.AspNetCore.dll", "lib/net7.0/Finbuckle.MultiTenant.AspNetCore.xml", "lib/net8.0/Finbuckle.MultiTenant.AspNetCore.dll", "lib/net8.0/Finbuckle.MultiTenant.AspNetCore.xml"]}, "Finbuckle.MultiTenant.EntityFrameworkCore/6.13.1": {"sha512": "YjmTR4Mq3DKE2gzM6CYeCtXujYufuTONuc3GkVccFsIHZyDPmufbOfEtOJUKGzXjmRbIVC3vTn/SwekZOxuiWg==", "type": "package", "path": "finbuckle.multitenant.entityframeworkcore/6.13.1", "files": [".nupkg.metadata", ".signature.p7s", "finbuckle-128x128.png", "finbuckle.multitenant.entityframeworkcore.6.13.1.nupkg.sha512", "finbuckle.multitenant.entityframeworkcore.nuspec", "lib/net6.0/Finbuckle.MultiTenant.EntityFrameworkCore.dll", "lib/net6.0/Finbuckle.MultiTenant.EntityFrameworkCore.xml", "lib/net7.0/Finbuckle.MultiTenant.EntityFrameworkCore.dll", "lib/net7.0/Finbuckle.MultiTenant.EntityFrameworkCore.xml", "lib/net8.0/Finbuckle.MultiTenant.EntityFrameworkCore.dll", "lib/net8.0/Finbuckle.MultiTenant.EntityFrameworkCore.xml"]}, "FluentValidation/11.9.0": {"sha512": "VneVlTvwYDkfHV5av3QrQ0amALgrLX6LV94wlYyEsh0B/klJBW7C8y2eAtj5tOZ3jH6CAVpr4s1ZGgew/QWyig==", "type": "package", "path": "fluentvalidation/11.9.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "fluent-validation-icon.png", "fluentvalidation.11.9.0.nupkg.sha512", "fluentvalidation.nuspec", "lib/net5.0/FluentValidation.dll", "lib/net5.0/FluentValidation.xml", "lib/net6.0/FluentValidation.dll", "lib/net6.0/FluentValidation.xml", "lib/net7.0/FluentValidation.dll", "lib/net7.0/FluentValidation.xml", "lib/net8.0/FluentValidation.dll", "lib/net8.0/FluentValidation.xml", "lib/netstandard2.0/FluentValidation.dll", "lib/netstandard2.0/FluentValidation.xml", "lib/netstandard2.1/FluentValidation.dll", "lib/netstandard2.1/FluentValidation.xml"]}, "FluentValidation.DependencyInjectionExtensions/11.9.0": {"sha512": "Ko++xvN7HUf4WlHJL6bhsybUj/uho8ApOYIdxGjpF8Ot7Fukz6LRfRJ06H0KXhWqmMHWEbu89hJbjKJHtg7b9g==", "type": "package", "path": "fluentvalidation.dependencyinjectionextensions/11.9.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "fluent-validation-icon.png", "fluentvalidation.dependencyinjectionextensions.11.9.0.nupkg.sha512", "fluentvalidation.dependencyinjectionextensions.nuspec", "lib/netstandard2.0/FluentValidation.DependencyInjectionExtensions.dll", "lib/netstandard2.0/FluentValidation.DependencyInjectionExtensions.xml", "lib/netstandard2.1/FluentValidation.DependencyInjectionExtensions.dll", "lib/netstandard2.1/FluentValidation.DependencyInjectionExtensions.xml"]}, "Hangfire/1.8.15": {"sha512": "yUu3JuoBDsU3Bbs8NTs1XapbGUiLzC8WJUm5z8rPoMyEWxgZRPlWvx+I85mFddQutveieoKKxAdkmgi+M89htA==", "type": "package", "path": "hangfire/1.8.15", "files": [".nupkg.metadata", ".signature.p7s", "COPYING", "COPYING.LESSER", "LICENSE.md", "LICENSE_ROYALTYFREE", "LICENSE_STANDARD", "NOTICES", "README.md", "hangfire.1.8.15.nupkg.sha512", "hangfire.nuspec", "icon.png"]}, "Hangfire.AspNetCore/1.8.15": {"sha512": "o85rtOYvhbWpNUGT4YrZE62lugShfdL3EMCqX2QoTC6eXVwpqtWOYfSeiTXAxlbF0CXNJJsuSCISKLmzbngWAA==", "type": "package", "path": "hangfire.aspnetcore/1.8.15", "files": [".nupkg.metadata", ".signature.p7s", "COPYING", "COPYING.LESSER", "LICENSE.md", "LICENSE_ROYALTYFREE", "LICENSE_STANDARD", "NOTICES", "hangfire.aspnetcore.1.8.15.nupkg.sha512", "hangfire.aspnetcore.nuspec", "icon.png", "lib/net451/Hangfire.AspNetCore.dll", "lib/net451/Hangfire.AspNetCore.xml", "lib/net461/Hangfire.AspNetCore.dll", "lib/net461/Hangfire.AspNetCore.xml", "lib/netcoreapp3.0/Hangfire.AspNetCore.dll", "lib/netcoreapp3.0/Hangfire.AspNetCore.xml", "lib/netstandard1.3/Hangfire.AspNetCore.dll", "lib/netstandard1.3/Hangfire.AspNetCore.xml", "lib/netstandard2.0/Hangfire.AspNetCore.dll", "lib/netstandard2.0/Hangfire.AspNetCore.xml"]}, "Hangfire.Console/1.4.2": {"sha512": "N1KeWrvVsJt2jnA6hA9Kid/6CnUMZdC0fVmDpyFAnTAdXrFdpTwvhGwfzoiUWD8boZweA3BvK6CFoZoJhjQTEg==", "type": "package", "path": "hangfire.console/1.4.2", "files": [".nupkg.metadata", ".signature.p7s", "hangfire.console.1.4.2.nupkg.sha512", "hangfire.console.nuspec", "lib/net45/Hangfire.Console.dll", "lib/net45/Hangfire.Console.xml", "lib/netstandard1.3/Hangfire.Console.dll", "lib/netstandard1.3/Hangfire.Console.xml"]}, "Hangfire.Console.Extensions/1.0.5": {"sha512": "5PpMP86s1HJLSJr1tRu8y8NEZGdAZmiaICGdVIlJM/jfAVcTbc0pT3osc7MT0S/ZRoUlwk1ErVWgxARNhtP9aQ==", "type": "package", "path": "hangfire.console.extensions/1.0.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "hangfire.console.extensions.1.0.5.nupkg.sha512", "hangfire.console.extensions.nuspec", "lib/netstandard2.0/Hangfire.Console.Extensions.dll"]}, "Hangfire.Core/1.8.15": {"sha512": "+w8gT6CFH4jicVEsJ8WlMRJMNV2MG52JNtvKoXPFHFs6nkDTND6iDeCjydyHgp+85lZPRXc+s9/vkxD2vbPrLg==", "type": "package", "path": "hangfire.core/1.8.15", "files": [".nupkg.metadata", ".signature.p7s", "COPYING", "COPYING.LESSER", "LICENSE.md", "LICENSE_ROYALTYFREE", "LICENSE_STANDARD", "NOTICES", "README.md", "hangfire.core.1.8.15.nupkg.sha512", "hangfire.core.nuspec", "icon.png", "lib/net451/Hangfire.Core.dll", "lib/net451/Hangfire.Core.xml", "lib/net451/ca/Hangfire.Core.resources.dll", "lib/net451/de/Hangfire.Core.resources.dll", "lib/net451/es/Hangfire.Core.resources.dll", "lib/net451/fa/Hangfire.Core.resources.dll", "lib/net451/fr/Hangfire.Core.resources.dll", "lib/net451/nb/Hangfire.Core.resources.dll", "lib/net451/nl/Hangfire.Core.resources.dll", "lib/net451/pt-BR/Hangfire.Core.resources.dll", "lib/net451/pt-PT/Hangfire.Core.resources.dll", "lib/net451/pt/Hangfire.Core.resources.dll", "lib/net451/sv/Hangfire.Core.resources.dll", "lib/net451/tr-TR/Hangfire.Core.resources.dll", "lib/net451/zh-TW/Hangfire.Core.resources.dll", "lib/net451/zh/Hangfire.Core.resources.dll", "lib/net46/Hangfire.Core.dll", "lib/net46/Hangfire.Core.xml", "lib/net46/ca/Hangfire.Core.resources.dll", "lib/net46/de/Hangfire.Core.resources.dll", "lib/net46/es/Hangfire.Core.resources.dll", "lib/net46/fa/Hangfire.Core.resources.dll", "lib/net46/fr/Hangfire.Core.resources.dll", "lib/net46/nb/Hangfire.Core.resources.dll", "lib/net46/nl/Hangfire.Core.resources.dll", "lib/net46/pt-BR/Hangfire.Core.resources.dll", "lib/net46/pt-PT/Hangfire.Core.resources.dll", "lib/net46/pt/Hangfire.Core.resources.dll", "lib/net46/sv/Hangfire.Core.resources.dll", "lib/net46/tr-TR/Hangfire.Core.resources.dll", "lib/net46/zh-TW/Hangfire.Core.resources.dll", "lib/net46/zh/Hangfire.Core.resources.dll", "lib/netstandard1.3/Hangfire.Core.dll", "lib/netstandard1.3/Hangfire.Core.xml", "lib/netstandard1.3/ca/Hangfire.Core.resources.dll", "lib/netstandard1.3/de/Hangfire.Core.resources.dll", "lib/netstandard1.3/es/Hangfire.Core.resources.dll", "lib/netstandard1.3/fa/Hangfire.Core.resources.dll", "lib/netstandard1.3/fr/Hangfire.Core.resources.dll", "lib/netstandard1.3/nb/Hangfire.Core.resources.dll", "lib/netstandard1.3/nl/Hangfire.Core.resources.dll", "lib/netstandard1.3/pt-BR/Hangfire.Core.resources.dll", "lib/netstandard1.3/pt-PT/Hangfire.Core.resources.dll", "lib/netstandard1.3/pt/Hangfire.Core.resources.dll", "lib/netstandard1.3/sv/Hangfire.Core.resources.dll", "lib/netstandard1.3/tr-TR/Hangfire.Core.resources.dll", "lib/netstandard1.3/zh-TW/Hangfire.Core.resources.dll", "lib/netstandard1.3/zh/Hangfire.Core.resources.dll", "lib/netstandard2.0/Hangfire.Core.dll", "lib/netstandard2.0/Hangfire.Core.xml", "lib/netstandard2.0/ca/Hangfire.Core.resources.dll", "lib/netstandard2.0/de/Hangfire.Core.resources.dll", "lib/netstandard2.0/es/Hangfire.Core.resources.dll", "lib/netstandard2.0/fa/Hangfire.Core.resources.dll", "lib/netstandard2.0/fr/Hangfire.Core.resources.dll", "lib/netstandard2.0/nb/Hangfire.Core.resources.dll", "lib/netstandard2.0/nl/Hangfire.Core.resources.dll", "lib/netstandard2.0/pt-BR/Hangfire.Core.resources.dll", "lib/netstandard2.0/pt-PT/Hangfire.Core.resources.dll", "lib/netstandard2.0/pt/Hangfire.Core.resources.dll", "lib/netstandard2.0/sv/Hangfire.Core.resources.dll", "lib/netstandard2.0/tr-TR/Hangfire.Core.resources.dll", "lib/netstandard2.0/zh-TW/Hangfire.Core.resources.dll", "lib/netstandard2.0/zh/Hangfire.Core.resources.dll"]}, "Hangfire.Dashboard.Basic.Authentication/5.0.0": {"sha512": "yB+3BnV4i15mDBxinpCzUAO15Ks6wme8tGotDb1cnxrcd1EDGtQ4MFkidXMVolAiMgc3uL+nYUu6kIMwl9u5Bg==", "type": "package", "path": "hangfire.dashboard.basic.authentication/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "hangfire.dashboard.basic.authentication.5.0.0.nupkg.sha512", "hangfire.dashboard.basic.authentication.nuspec", "lib/net5.0/HangfireBasicAuthenticationFilter.dll"]}, "Hangfire.NetCore/1.8.15": {"sha512": "HNACpklY1FGcsCr/xlPvmh5R5JqH2eEBxOp63Dwph6H6LdGWWqHoMpxjxkpYkZXM2mNpmk+j0Dk8lizadfnD+A==", "type": "package", "path": "hangfire.netcore/1.8.15", "files": [".nupkg.metadata", ".signature.p7s", "COPYING", "COPYING.LESSER", "LICENSE.md", "LICENSE_ROYALTYFREE", "LICENSE_STANDARD", "NOTICES", "hangfire.netcore.1.8.15.nupkg.sha512", "hangfire.netcore.nuspec", "icon.png", "lib/net451/Hangfire.NetCore.dll", "lib/net451/Hangfire.NetCore.xml", "lib/net461/Hangfire.NetCore.dll", "lib/net461/Hangfire.NetCore.xml", "lib/netstandard1.3/Hangfire.NetCore.dll", "lib/netstandard1.3/Hangfire.NetCore.xml", "lib/netstandard2.0/Hangfire.NetCore.dll", "lib/netstandard2.0/Hangfire.NetCore.xml", "lib/netstandard2.1/Hangfire.NetCore.dll", "lib/netstandard2.1/Hangfire.NetCore.xml"]}, "Hangfire.PostgreSql/1.20.10": {"sha512": "Nn/88KoBvmy/xyopC9s+lXkwxQ6VB+RKyM8tjX3EgfSARDFxl2sEsFu0lw7WrjFdosg+E3naGzM5MzyiiL5i6w==", "type": "package", "path": "hangfire.postgresql/1.20.10", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "hangfire.postgresql.1.20.10.nupkg.sha512", "hangfire.postgresql.nuspec", "lib/netstandard2.0/Hangfire.PostgreSql.dll", "lib/netstandard2.0/Hangfire.PostgreSql.xml"]}, "Hangfire.SqlServer/1.8.15": {"sha512": "bNN0jpw+RdUlFK5UaXz+VhfSIV1Os264XDettv9t6nY9hP2afDzRXWYAuFNamKH6s+oFThGCZXbULZTDAPjASQ==", "type": "package", "path": "hangfire.sqlserver/1.8.15", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "COPYING", "COPYING.LESSER", "LICENSE.md", "LICENSE_ROYALTYFREE", "LICENSE_STANDARD", "NOTICES", "hangfire.sqlserver.1.8.15.nupkg.sha512", "hangfire.sqlserver.nuspec", "icon.png", "lib/net451/Hangfire.SqlServer.dll", "lib/net451/Hangfire.SqlServer.xml", "lib/netstandard1.3/Hangfire.SqlServer.dll", "lib/netstandard1.3/Hangfire.SqlServer.xml", "lib/netstandard2.0/Hangfire.SqlServer.dll", "lib/netstandard2.0/Hangfire.SqlServer.xml", "tools/install.sql"]}, "Humanizer.Core/2.14.1": {"sha512": "lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "type": "package", "path": "humanizer.core/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.2.14.1.nupkg.sha512", "humanizer.core.nuspec", "lib/net6.0/Humanizer.dll", "lib/net6.0/Humanizer.xml", "lib/netstandard1.0/Humanizer.dll", "lib/netstandard1.0/Humanizer.xml", "lib/netstandard2.0/Humanizer.dll", "lib/netstandard2.0/Humanizer.xml", "logo.png"]}, "Mapster/7.4.0": {"sha512": "RYGoDqvS4WTKIq0HDyPBBVIj6N0mluOCXQ1Vk95JKseMHEsbCXSEGKSlP95oL+s42IXAXbqvHj7p0YaRBUcfqg==", "type": "package", "path": "mapster/7.4.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net6.0/Mapster.dll", "lib/net7.0/Mapster.dll", "mapster.7.4.0.nupkg.sha512", "mapster.nuspec"]}, "Mapster.Core/1.2.1": {"sha512": "11lokmfliBEMMmjeqxFGNpqGXq6tN96zFqpBmfYeahr4Ybk63oDmeJmOflsATjobYkX248g5Y62oQ2NNnZaeww==", "type": "package", "path": "mapster.core/1.2.1", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net6.0/Mapster.Core.dll", "lib/net7.0/Mapster.Core.dll", "mapster.core.1.2.1.nupkg.sha512", "mapster.core.nuspec"]}, "Mapster.DependencyInjection/1.0.1": {"sha512": "LfjnRIwx6WAo3ssq8PFeaHFaUz00BfSG9BhWgXsiDa3H5lDhG0lpMGDF6w2ZnooS4eHYmAv4f77VxmzpvgorNg==", "type": "package", "path": "mapster.dependencyinjection/1.0.1", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net6.0/Mapster.DependencyInjection.dll", "lib/net7.0/Mapster.DependencyInjection.dll", "mapster.dependencyinjection.1.0.1.nupkg.sha512", "mapster.dependencyinjection.nuspec"]}, "MediatR/12.2.0": {"sha512": "8TUFrHapKi6D74PhnSNEguRsH91HNGyP3R4ZQdgDorJgl9Wac5Prh0vA33QfrniAaS6L2xNNhc6vxzg+5AIbwA==", "type": "package", "path": "mediatr/12.2.0", "files": [".nupkg.metadata", ".signature.p7s", "gradient_128x128.png", "lib/net6.0/MediatR.dll", "lib/net6.0/MediatR.xml", "lib/netstandard2.0/MediatR.dll", "lib/netstandard2.0/MediatR.xml", "mediatr.12.2.0.nupkg.sha512", "mediatr.nuspec"]}, "MediatR.Contracts/2.0.1": {"sha512": "FYv95bNT4UwcNA+G/J1oX5OpRiSUxteXaUt2BJbRSdRNiIUNbggJF69wy6mnk2wYToaanpdXZdCwVylt96MpwQ==", "type": "package", "path": "mediatr.contracts/2.0.1", "files": [".nupkg.metadata", ".signature.p7s", "gradient_128x128.png", "lib/netstandard2.0/MediatR.Contracts.dll", "lib/netstandard2.0/MediatR.Contracts.xml", "mediatr.contracts.2.0.1.nupkg.sha512", "mediatr.contracts.nuspec"]}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.10": {"sha512": "rcPXghZCc82IB9U2Px1Ln5Zn3vjV4p83H/Few5T/904hBddjSz03COQ2zOGWBBvdTBY+GciAUJwgBFNWaxLfqw==", "type": "package", "path": "microsoft.aspnetcore.authentication.jwtbearer/8.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net8.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll", "lib/net8.0/Microsoft.AspNetCore.Authentication.JwtBearer.xml", "microsoft.aspnetcore.authentication.jwtbearer.8.0.10.nupkg.sha512", "microsoft.aspnetcore.authentication.jwtbearer.nuspec"]}, "Microsoft.AspNetCore.Authentication.OpenIdConnect/8.0.1": {"sha512": "gx344DYqTjhotuG56JchlqArEvytZGgYuIuaNP/B+FcvXMuexO3mujamvs4qWXH+5g96poi2+p/Cek5KpVmaQA==", "type": "package", "path": "microsoft.aspnetcore.authentication.openidconnect/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net8.0/Microsoft.AspNetCore.Authentication.OpenIdConnect.dll", "lib/net8.0/Microsoft.AspNetCore.Authentication.OpenIdConnect.xml", "microsoft.aspnetcore.authentication.openidconnect.8.0.1.nupkg.sha512", "microsoft.aspnetcore.authentication.openidconnect.nuspec"]}, "Microsoft.AspNetCore.Connections.Abstractions/8.0.10": {"sha512": "7fqSpSGzBRldyO1obKMPGYUdU4iwvMPsUEgdufgRZe2tBF9rKf8ffGmuObP81N4AKb5A438+5ER5t3ES0r1yDQ==", "type": "package", "path": "microsoft.aspnetcore.connections.abstractions/8.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.AspNetCore.Connections.Abstractions.dll", "lib/net462/Microsoft.AspNetCore.Connections.Abstractions.xml", "lib/net8.0/Microsoft.AspNetCore.Connections.Abstractions.dll", "lib/net8.0/Microsoft.AspNetCore.Connections.Abstractions.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Connections.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Connections.Abstractions.xml", "lib/netstandard2.1/Microsoft.AspNetCore.Connections.Abstractions.dll", "lib/netstandard2.1/Microsoft.AspNetCore.Connections.Abstractions.xml", "microsoft.aspnetcore.connections.abstractions.8.0.10.nupkg.sha512", "microsoft.aspnetcore.connections.abstractions.nuspec"]}, "Microsoft.AspNetCore.Cryptography.Internal/8.0.10": {"sha512": "MT/jvNoiXUB82drzqtqZqyAfxQH2b0kpEyjjMYrSLmqgAvBkMEKJelbqHazEo5Lxtq43uquPgeBtTuSrVog5lQ==", "type": "package", "path": "microsoft.aspnetcore.cryptography.internal/8.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.AspNetCore.Cryptography.Internal.dll", "lib/net462/Microsoft.AspNetCore.Cryptography.Internal.xml", "lib/net8.0/Microsoft.AspNetCore.Cryptography.Internal.dll", "lib/net8.0/Microsoft.AspNetCore.Cryptography.Internal.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Cryptography.Internal.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Cryptography.Internal.xml", "microsoft.aspnetcore.cryptography.internal.8.0.10.nupkg.sha512", "microsoft.aspnetcore.cryptography.internal.nuspec"]}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/8.0.10": {"sha512": "4jd0g3k2R1L1bhhpVmJOp7rAs76V9XLVuhl8J3sTAcl2dKMS78PsKG1HX75U73WEEwrsM4Bui2/N1/Blwgt5iw==", "type": "package", "path": "microsoft.aspnetcore.cryptography.keyderivation/8.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll", "lib/net462/Microsoft.AspNetCore.Cryptography.KeyDerivation.xml", "lib/net8.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll", "lib/net8.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.xml", "microsoft.aspnetcore.cryptography.keyderivation.8.0.10.nupkg.sha512", "microsoft.aspnetcore.cryptography.keyderivation.nuspec"]}, "Microsoft.AspNetCore.DataProtection/8.0.1": {"sha512": "DX2Quy+WRLXPGeWNGtOKyPT2vusNfFI15S9M2DIpaeBqZmXpheoQdlvsPzyn7K8uorcnfUW5ML/vSo26FOQ9BA==", "type": "package", "path": "microsoft.aspnetcore.dataprotection/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.AspNetCore.DataProtection.dll", "lib/net462/Microsoft.AspNetCore.DataProtection.xml", "lib/net8.0/Microsoft.AspNetCore.DataProtection.dll", "lib/net8.0/Microsoft.AspNetCore.DataProtection.xml", "lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.dll", "lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.xml", "microsoft.aspnetcore.dataprotection.8.0.1.nupkg.sha512", "microsoft.aspnetcore.dataprotection.nuspec"]}, "Microsoft.AspNetCore.DataProtection.Abstractions/8.0.1": {"sha512": "4n79+eJnSXaqZIx5c6A+Dtl2bIYwcrAujKDfnDJnTkJa0n5NH4UBCCDNEyONW11UeBYzZb1G4DTE7YWOFbw+9Q==", "type": "package", "path": "microsoft.aspnetcore.dataprotection.abstractions/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.AspNetCore.DataProtection.Abstractions.dll", "lib/net462/Microsoft.AspNetCore.DataProtection.Abstractions.xml", "lib/net8.0/Microsoft.AspNetCore.DataProtection.Abstractions.dll", "lib/net8.0/Microsoft.AspNetCore.DataProtection.Abstractions.xml", "lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.Abstractions.xml", "microsoft.aspnetcore.dataprotection.abstractions.8.0.1.nupkg.sha512", "microsoft.aspnetcore.dataprotection.abstractions.nuspec"]}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/8.0.10": {"sha512": "vMeY9F3Sq+AiZlquf84rwHOAQBS8nb8kd1RcuoXKPBhHNGBxMLYnr8/e/FCwu7kb14hH/rqWoEuyO4WXpAO6Rw==", "type": "package", "path": "microsoft.aspnetcore.identity.entityframeworkcore/8.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net8.0/Microsoft.AspNetCore.Identity.EntityFrameworkCore.dll", "lib/net8.0/Microsoft.AspNetCore.Identity.EntityFrameworkCore.xml", "microsoft.aspnetcore.identity.entityframeworkcore.8.0.10.nupkg.sha512", "microsoft.aspnetcore.identity.entityframeworkcore.nuspec"]}, "Microsoft.AspNetCore.Mvc.Versioning/5.1.0": {"sha512": "UX8w9BlCiZpr6Ox4YAve1w0CkI1CAovukGNzKd7v0+5pZc8lzuG5tRovucr1RWIKHs0E/Yx8563CN7KzaB3bpw==", "type": "package", "path": "microsoft.aspnetcore.mvc.versioning/5.1.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "icon.png", "lib/net5.0/Microsoft.AspNetCore.Mvc.Versioning.dll", "lib/net5.0/Microsoft.AspNetCore.Mvc.Versioning.pdb", "lib/net5.0/Microsoft.AspNetCore.Mvc.Versioning.xml", "lib/net6.0/Microsoft.AspNetCore.Mvc.Versioning.dll", "lib/net6.0/Microsoft.AspNetCore.Mvc.Versioning.pdb", "lib/net6.0/Microsoft.AspNetCore.Mvc.Versioning.xml", "lib/netcoreapp3.1/Microsoft.AspNetCore.Mvc.Versioning.dll", "lib/netcoreapp3.1/Microsoft.AspNetCore.Mvc.Versioning.pdb", "lib/netcoreapp3.1/Microsoft.AspNetCore.Mvc.Versioning.xml", "microsoft.aspnetcore.mvc.versioning.5.1.0.nupkg.sha512", "microsoft.aspnetcore.mvc.versioning.nuspec"]}, "Microsoft.AspNetCore.OpenApi/8.0.11": {"sha512": "1WCvCZpqvOAyul6upSJ8Rkb/QHdr4PLrDny26vFexya/nTkG3x2zt8j9qlJ5T3J+/yJD9KwlGKhho9ZDD/YiFA==", "type": "package", "path": "microsoft.aspnetcore.openapi/8.0.11", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net8.0/Microsoft.AspNetCore.OpenApi.dll", "lib/net8.0/Microsoft.AspNetCore.OpenApi.xml", "microsoft.aspnetcore.openapi.8.0.11.nupkg.sha512", "microsoft.aspnetcore.openapi.nuspec"]}, "Microsoft.AspNetCore.SignalR.Client.Core/8.0.10": {"sha512": "JvteyIThzPPSxzGM5XDn8eSYAGaH4sr1tztXhH5r080VEK8AhT7OnTIX/B3xBOKA6nwf1J6Ic9/GygekduWRUw==", "type": "package", "path": "microsoft.aspnetcore.signalr.client.core/8.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.AspNetCore.SignalR.Client.Core.dll", "lib/net462/Microsoft.AspNetCore.SignalR.Client.Core.xml", "lib/net8.0/Microsoft.AspNetCore.SignalR.Client.Core.dll", "lib/net8.0/Microsoft.AspNetCore.SignalR.Client.Core.xml", "lib/netstandard2.0/Microsoft.AspNetCore.SignalR.Client.Core.dll", "lib/netstandard2.0/Microsoft.AspNetCore.SignalR.Client.Core.xml", "lib/netstandard2.1/Microsoft.AspNetCore.SignalR.Client.Core.dll", "lib/netstandard2.1/Microsoft.AspNetCore.SignalR.Client.Core.xml", "microsoft.aspnetcore.signalr.client.core.8.0.10.nupkg.sha512", "microsoft.aspnetcore.signalr.client.core.nuspec"]}, "Microsoft.AspNetCore.SignalR.Common/8.0.10": {"sha512": "co7C/W6Is0DHB3goFHZbTLw1LSgo6uOhlGx36M1c9b2tPYBRlpqVBjuB8GVMLdZEPG4BCfJXBEmU/dU0Kdik8A==", "type": "package", "path": "microsoft.aspnetcore.signalr.common/8.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.AspNetCore.SignalR.Common.dll", "lib/net462/Microsoft.AspNetCore.SignalR.Common.xml", "lib/net8.0/Microsoft.AspNetCore.SignalR.Common.dll", "lib/net8.0/Microsoft.AspNetCore.SignalR.Common.xml", "lib/netstandard2.0/Microsoft.AspNetCore.SignalR.Common.dll", "lib/netstandard2.0/Microsoft.AspNetCore.SignalR.Common.xml", "microsoft.aspnetcore.signalr.common.8.0.10.nupkg.sha512", "microsoft.aspnetcore.signalr.common.nuspec"]}, "Microsoft.AspNetCore.SignalR.Protocols.Json/8.0.10": {"sha512": "ZyAboyGomA6cUWeLKiYExOaNKsrJiUvQHtyJ1n3xW/RLViRx0tHzCLzyvVJQJQnAIkqQOchbsdM2iMuGDAWI8w==", "type": "package", "path": "microsoft.aspnetcore.signalr.protocols.json/8.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.AspNetCore.SignalR.Protocols.Json.dll", "lib/net462/Microsoft.AspNetCore.SignalR.Protocols.Json.xml", "lib/net8.0/Microsoft.AspNetCore.SignalR.Protocols.Json.dll", "lib/net8.0/Microsoft.AspNetCore.SignalR.Protocols.Json.xml", "lib/netstandard2.0/Microsoft.AspNetCore.SignalR.Protocols.Json.dll", "lib/netstandard2.0/Microsoft.AspNetCore.SignalR.Protocols.Json.xml", "microsoft.aspnetcore.signalr.protocols.json.8.0.10.nupkg.sha512", "microsoft.aspnetcore.signalr.protocols.json.nuspec"]}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"sha512": "3aeMZ1N0lJoSyzqiP03hqemtb1BijhsJADdobn/4nsMJ8V1H+CrpuduUe4hlRdx+ikBQju1VGjMD1GJ3Sk05Eg==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Bcl.AsyncInterfaces.targets", "buildTransitive/net462/_._", "lib/net462/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net462/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.7.0.0.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Bcl.Cryptography/9.0.1": {"sha512": "U/tCi1nw6aflfp+H6Hzwgt+CIHx0PoKKOTF7wNaxIg3EgfzqV+DtUantwRNifRu0Hm0W3xnxZ0hOVZVoORLAyQ==", "type": "package", "path": "microsoft.bcl.cryptography/9.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Bcl.Cryptography.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Bcl.Cryptography.targets", "lib/net462/Microsoft.Bcl.Cryptography.dll", "lib/net462/Microsoft.Bcl.Cryptography.xml", "lib/net8.0/Microsoft.Bcl.Cryptography.dll", "lib/net8.0/Microsoft.Bcl.Cryptography.xml", "lib/net9.0/Microsoft.Bcl.Cryptography.dll", "lib/net9.0/Microsoft.Bcl.Cryptography.xml", "lib/netstandard2.0/Microsoft.Bcl.Cryptography.dll", "lib/netstandard2.0/Microsoft.Bcl.Cryptography.xml", "microsoft.bcl.cryptography.9.0.1.nupkg.sha512", "microsoft.bcl.cryptography.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Bcl.TimeProvider/8.0.1": {"sha512": "C7kWHJnMRY7EvJev2S8+yJHZ1y7A4ZlLbA4NE+O23BDIAN5mHeqND1m+SKv1ChRS5YlCDW7yAMUe7lttRsJaAA==", "type": "package", "path": "microsoft.bcl.timeprovider/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Bcl.TimeProvider.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Bcl.TimeProvider.targets", "lib/net462/Microsoft.Bcl.TimeProvider.dll", "lib/net462/Microsoft.Bcl.TimeProvider.xml", "lib/net8.0/Microsoft.Bcl.TimeProvider.dll", "lib/net8.0/Microsoft.Bcl.TimeProvider.xml", "lib/netstandard2.0/Microsoft.Bcl.TimeProvider.dll", "lib/netstandard2.0/Microsoft.Bcl.TimeProvider.xml", "microsoft.bcl.timeprovider.8.0.1.nupkg.sha512", "microsoft.bcl.timeprovider.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Build.Framework/17.8.3": {"sha512": "NrQZJW8TlKVPx72yltGb8SVz3P5mNRk9fNiD/ao8jRSk48WqIIdCn99q4IjlVmPcruuQ+yLdjNQLL8Rb4c916g==", "type": "package", "path": "microsoft.build.framework/17.8.3", "files": [".nupkg.metadata", ".signature.p7s", "MSBuild-NuGet-Icon.png", "README.md", "lib/net472/Microsoft.Build.Framework.dll", "lib/net472/Microsoft.Build.Framework.pdb", "lib/net472/Microsoft.Build.Framework.xml", "lib/net8.0/Microsoft.Build.Framework.dll", "lib/net8.0/Microsoft.Build.Framework.pdb", "lib/net8.0/Microsoft.Build.Framework.xml", "microsoft.build.framework.17.8.3.nupkg.sha512", "microsoft.build.framework.nuspec", "notices/THIRDPARTYNOTICES.txt", "ref/net472/Microsoft.Build.Framework.dll", "ref/net472/Microsoft.Build.Framework.xml", "ref/net8.0/Microsoft.Build.Framework.dll", "ref/net8.0/Microsoft.Build.Framework.xml", "ref/netstandard2.0/Microsoft.Build.Framework.dll", "ref/netstandard2.0/Microsoft.Build.Framework.xml"]}, "Microsoft.Build.Locator/1.7.8": {"sha512": "sPy10x527Ph16S2u0yGME4S6ohBKJ69WfjeGG/bvELYeZVmJdKjxgnlL8cJJJLGV/cZIRqSfB12UDB8ICakOog==", "type": "package", "path": "microsoft.build.locator/1.7.8", "files": [".nupkg.metadata", ".signature.p7s", "MSBuild-NuGet-Icon.png", "build/Microsoft.Build.Locator.props", "build/Microsoft.Build.Locator.targets", "lib/net46/Microsoft.Build.Locator.dll", "lib/net6.0/Microsoft.Build.Locator.dll", "microsoft.build.locator.1.7.8.nupkg.sha512", "microsoft.build.locator.nuspec"]}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {"sha512": "AxkxcPR+rheX0SmvpLVIGLhOUXAKG56a64kV9VQZ4y9gR9ZmPXnqZvHJnmwLSwzrEP6junUF11vuc+aqo5r68g==", "type": "package", "path": "microsoft.codeanalysis.analyzers/3.3.4", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.txt", "analyzers/dotnet/cs/Microsoft.CodeAnalysis.Analyzers.dll", "analyzers/dotnet/cs/Microsoft.CodeAnalysis.CSharp.Analyzers.dll", "analyzers/dotnet/cs/cs/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/de/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/es/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/it/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/Microsoft.CodeAnalysis.Analyzers.dll", "analyzers/dotnet/vb/Microsoft.CodeAnalysis.VisualBasic.Analyzers.dll", "analyzers/dotnet/vb/cs/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/de/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/es/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/fr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/it/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/ja/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/ko/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/pl/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/pt-BR/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/ru/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/tr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/zh-<PERSON>/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/zh-Hant/Microsoft.CodeAnalysis.Analyzers.resources.dll", "buildTransitive/Microsoft.CodeAnalysis.Analyzers.props", "buildTransitive/Microsoft.CodeAnalysis.Analyzers.targets", "buildTransitive/config/analysislevel_2_9_8_all.globalconfig", "buildTransitive/config/analysislevel_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevel_2_9_8_default.globalconfig", "buildTransitive/config/analysislevel_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevel_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevel_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevel_2_9_8_none.globalconfig", "buildTransitive/config/analysislevel_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevel_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevel_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_3_all.globalconfig", "buildTransitive/config/analysislevel_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_3_default.globalconfig", "buildTransitive/config/analysislevel_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevel_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_3_none.globalconfig", "buildTransitive/config/analysislevel_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevel_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_all.globalconfig", "buildTransitive/config/analysislevel_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_default.globalconfig", "buildTransitive/config/analysislevel_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_minimum.globalconfig", "buildTransitive/config/analysislevel_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_none.globalconfig", "buildTransitive/config/analysislevel_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_recommended.globalconfig", "buildTransitive/config/analysislevel_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_all.globalconfig", "buildTransitive/config/analysislevel_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_default.globalconfig", "buildTransitive/config/analysislevel_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_minimum.globalconfig", "buildTransitive/config/analysislevel_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_none.globalconfig", "buildTransitive/config/analysislevel_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_recommended.globalconfig", "buildTransitive/config/analysislevel_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevel_4_3_all.globalconfig", "buildTransitive/config/analysislevel_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevel_4_3_default.globalconfig", "buildTransitive/config/analysislevel_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevel_4_3_minimum.globalconfig", "buildTransitive/config/analysislevel_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevel_4_3_none.globalconfig", "buildTransitive/config/analysislevel_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevel_4_3_recommended.globalconfig", "buildTransitive/config/analysislevel_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_all.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_default.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_none.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_all.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_default.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_none.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_all.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_default.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_none.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_all.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_default.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_minimum.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_none.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_recommended.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_all.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_default.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_minimum.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_none.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_recommended.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_all.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_default.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_none.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_all.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_default.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_none.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_all.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_default.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_minimum.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_none.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_recommended.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_all.globalconfig", "buildTransitive/config/analysislevellibrary_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_default.globalconfig", "buildTransitive/config/analysislevellibrary_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_minimum.globalconfig", "buildTransitive/config/analysislevellibrary_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_none.globalconfig", "buildTransitive/config/analysislevellibrary_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_recommended.globalconfig", "buildTransitive/config/analysislevellibrary_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_all.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_default.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_minimum.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_none.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_recommended.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_recommended_warnaserror.globalconfig", "documentation/Analyzer Configuration.md", "documentation/Microsoft.CodeAnalysis.Analyzers.md", "documentation/Microsoft.CodeAnalysis.Analyzers.sarif", "editorconfig/AllRulesDefault/.editorconfig", "editorconfig/AllRulesDisabled/.editorconfig", "editorconfig/AllRulesEnabled/.editorconfig", "editorconfig/CorrectnessRulesDefault/.editorconfig", "editorconfig/CorrectnessRulesEnabled/.editorconfig", "editorconfig/DataflowRulesDefault/.editorconfig", "editorconfig/DataflowRulesEnabled/.editorconfig", "editorconfig/LibraryRulesDefault/.editorconfig", "editorconfig/LibraryRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCompatibilityRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCompatibilityRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCorrectnessRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCorrectnessRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDesignRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDesignRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDocumentationRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDocumentationRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisLocalizationRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisLocalizationRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisPerformanceRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisPerformanceRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisReleaseTrackingRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisReleaseTrackingRulesEnabled/.editorconfig", "editorconfig/PortedFromFxCopRulesDefault/.editorconfig", "editorconfig/PortedFromFxCopRulesEnabled/.editorconfig", "microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512", "microsoft.codeanalysis.analyzers.nuspec", "rulesets/AllRulesDefault.ruleset", "rulesets/AllRulesDisabled.ruleset", "rulesets/AllRulesEnabled.ruleset", "rulesets/CorrectnessRulesDefault.ruleset", "rulesets/CorrectnessRulesEnabled.ruleset", "rulesets/DataflowRulesDefault.ruleset", "rulesets/DataflowRulesEnabled.ruleset", "rulesets/LibraryRulesDefault.ruleset", "rulesets/LibraryRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisCompatibilityRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisCompatibilityRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisCorrectnessRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisCorrectnessRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisDesignRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisDesignRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisDocumentationRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisDocumentationRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisLocalizationRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisLocalizationRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisPerformanceRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisPerformanceRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisReleaseTrackingRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisReleaseTrackingRulesEnabled.ruleset", "rulesets/PortedFromFxCopRulesDefault.ruleset", "rulesets/PortedFromFxCopRulesEnabled.ruleset", "tools/install.ps1", "tools/uninstall.ps1"]}, "Microsoft.CodeAnalysis.Common/4.8.0": {"sha512": "/jR+e/9aT+BApoQJABlVCKnnggGQbvGh7BKq2/wI1LamxC+LbzhcLj4Vj7gXCofl1n4E521YfF9w0WcASGg/KA==", "type": "package", "path": "microsoft.codeanalysis.common/4.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/net6.0/Microsoft.CodeAnalysis.dll", "lib/net6.0/Microsoft.CodeAnalysis.pdb", "lib/net6.0/Microsoft.CodeAnalysis.xml", "lib/net6.0/cs/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/de/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/es/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/fr/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/it/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/ja/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/ko/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/pl/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/pt-BR/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/ru/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/tr/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/Microsoft.CodeAnalysis.dll", "lib/net7.0/Microsoft.CodeAnalysis.pdb", "lib/net7.0/Microsoft.CodeAnalysis.xml", "lib/net7.0/cs/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/de/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/es/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/fr/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/it/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/ja/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/ko/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/pl/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/ru/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/tr/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll", "microsoft.codeanalysis.common.4.8.0.nupkg.sha512", "microsoft.codeanalysis.common.nuspec"]}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"sha512": "+3+qfdb/aaGD8PZRCrsdobbzGs1m9u119SkkJt8e/mk3xLJz/udLtS2T6nY27OTXxBBw10HzAbC8Z9w08VyP/g==", "type": "package", "path": "microsoft.codeanalysis.csharp/4.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/net6.0/Microsoft.CodeAnalysis.CSharp.dll", "lib/net6.0/Microsoft.CodeAnalysis.CSharp.pdb", "lib/net6.0/Microsoft.CodeAnalysis.CSharp.xml", "lib/net6.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/Microsoft.CodeAnalysis.CSharp.dll", "lib/net7.0/Microsoft.CodeAnalysis.CSharp.pdb", "lib/net7.0/Microsoft.CodeAnalysis.CSharp.xml", "lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll", "microsoft.codeanalysis.csharp.4.8.0.nupkg.sha512", "microsoft.codeanalysis.csharp.nuspec"]}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"sha512": "3amm4tq4Lo8/BGvg9p3BJh3S9nKq2wqCXfS7138i69TUpo/bD+XvD0hNurpEBtcNZhi1FyutiomKJqVF39ugYA==", "type": "package", "path": "microsoft.codeanalysis.csharp.workspaces/4.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/net6.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll", "lib/net6.0/Microsoft.CodeAnalysis.CSharp.Workspaces.pdb", "lib/net6.0/Microsoft.CodeAnalysis.CSharp.Workspaces.xml", "lib/net6.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll", "lib/net7.0/Microsoft.CodeAnalysis.CSharp.Workspaces.pdb", "lib/net7.0/Microsoft.CodeAnalysis.CSharp.Workspaces.xml", "lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.Workspaces.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.Workspaces.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "microsoft.codeanalysis.csharp.workspaces.4.8.0.nupkg.sha512", "microsoft.codeanalysis.csharp.workspaces.nuspec"]}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"sha512": "LXyV+MJKsKRu3FGJA3OmSk40OUIa/dQCFLOnm5X8MNcujx7hzGu8o+zjXlb/cy5xUdZK2UKYb9YaQ2E8m9QehQ==", "type": "package", "path": "microsoft.codeanalysis.workspaces.common/4.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/net6.0/Microsoft.CodeAnalysis.Workspaces.dll", "lib/net6.0/Microsoft.CodeAnalysis.Workspaces.pdb", "lib/net6.0/Microsoft.CodeAnalysis.Workspaces.xml", "lib/net6.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/zh-<PERSON>/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.dll", "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.pdb", "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.xml", "lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/zh-<PERSON>/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.Workspaces.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.Workspaces.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.Workspaces.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll", "microsoft.codeanalysis.workspaces.common.4.8.0.nupkg.sha512", "microsoft.codeanalysis.workspaces.common.nuspec"]}, "Microsoft.CodeAnalysis.Workspaces.MSBuild/4.8.0": {"sha512": "IEYreI82QZKklp54yPHxZNG9EKSK6nHEkeuf+0Asie9llgS1gp0V1hw7ODG+QyoB7MuAnNQHmeV1Per/ECpv6A==", "type": "package", "path": "microsoft.codeanalysis.workspaces.msbuild/4.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/net472/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.exe", "lib/net472/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.pdb", "lib/net472/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.xml", "lib/net472/Microsoft.CodeAnalysis.Workspaces.MSBuild.dll", "lib/net472/Microsoft.CodeAnalysis.Workspaces.MSBuild.pdb", "lib/net472/Microsoft.CodeAnalysis.Workspaces.MSBuild.xml", "lib/net472/cs/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net472/de/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net472/es/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net472/fr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net472/it/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net472/ja/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net472/ko/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net472/pl/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net472/pt-BR/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net472/ru/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net472/tr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net472/zh-<PERSON>/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net472/zh-Hant/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net6.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll", "lib/net6.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.pdb", "lib/net6.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.runtimeconfig.json", "lib/net6.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.xml", "lib/net6.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.dll", "lib/net6.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.pdb", "lib/net6.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.xml", "lib/net6.0/cs/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net6.0/de/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net6.0/es/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net6.0/fr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net6.0/it/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net6.0/ja/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net6.0/ko/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net6.0/pl/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net6.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net6.0/ru/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net6.0/tr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net6.0/zh-<PERSON>/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net6.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll", "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.pdb", "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.runtimeconfig.json", "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.xml", "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.dll", "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.pdb", "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.xml", "lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net7.0/zh-<PERSON>/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "microsoft.codeanalysis.workspaces.msbuild.4.8.0.nupkg.sha512", "microsoft.codeanalysis.workspaces.msbuild.nuspec"]}, "Microsoft.EntityFrameworkCore/9.0.5": {"sha512": "TeCtb/vc+jxvgkVAqeJlZKOoG5w/w8AigWQQyOmeJsJ7+0SkONX8bqEV/wB+ojnT0sXuJrrfXQOEC3ws6asEng==", "type": "package", "path": "microsoft.entityframeworkcore/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net8.0/Microsoft.EntityFrameworkCore.props", "lib/net8.0/Microsoft.EntityFrameworkCore.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.xml", "microsoft.entityframeworkcore.9.0.5.nupkg.sha512", "microsoft.entityframeworkcore.nuspec"]}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.5": {"sha512": "81fGyIibhGc4rq4ZxmVZE/1CFSvGMQOZqdRyCBLKz/Hb8eE973dmSfcdXpXhQ/5f+nbax4VGkWhwPGxWUNWaCQ==", "type": "package", "path": "microsoft.entityframeworkcore.abstractions/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.xml", "microsoft.entityframeworkcore.abstractions.9.0.5.nupkg.sha512", "microsoft.entityframeworkcore.abstractions.nuspec"]}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.5": {"sha512": "kWRrD69qCXo7lahPZPt7C127UfK0I024laFZEDMfT3JbALB1EWneFvq1utWM0cNKPFuYis1E1oaYTuRGI/9inQ==", "type": "package", "path": "microsoft.entityframeworkcore.analyzers/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "analyzers/dotnet/cs/Microsoft.EntityFrameworkCore.Analyzers.dll", "docs/PACKAGE.md", "microsoft.entityframeworkcore.analyzers.9.0.5.nupkg.sha512", "microsoft.entityframeworkcore.analyzers.nuspec"]}, "Microsoft.EntityFrameworkCore.Design/9.0.5": {"sha512": "xOVWCGRF8DpOIoZ196/g7bdghc2e7Fp6R2vZPKndWv8A64bSDSaS7F2CUoqZpmSphUeT+1HDRpNYFRBQd8H71g==", "type": "package", "path": "microsoft.entityframeworkcore.design/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "build/net8.0/Microsoft.EntityFrameworkCore.Design.props", "lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.Design.xml", "microsoft.entityframeworkcore.design.9.0.5.nupkg.sha512", "microsoft.entityframeworkcore.design.nuspec"]}, "Microsoft.EntityFrameworkCore.Relational/9.0.5": {"sha512": "6eErbrZFd9yNnncemtDdmHZ3KC792OQCIYITuMsjK2oh4CLzlYo8mzNsozgUzQ+utHnne11/3eV8zMWbYF5Puw==", "type": "package", "path": "microsoft.entityframeworkcore.relational/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.Relational.xml", "microsoft.entityframeworkcore.relational.9.0.5.nupkg.sha512", "microsoft.entityframeworkcore.relational.nuspec"]}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"sha512": "Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "type": "package", "path": "microsoft.extensions.apidescription.server/6.0.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/Microsoft.Extensions.ApiDescription.Server.props", "build/Microsoft.Extensions.ApiDescription.Server.targets", "buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.props", "buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.targets", "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512", "microsoft.extensions.apidescription.server.nuspec", "tools/Newtonsoft.Json.dll", "tools/dotnet-getdocument.deps.json", "tools/dotnet-getdocument.dll", "tools/dotnet-getdocument.runtimeconfig.json", "tools/net461-x86/GetDocument.Insider.exe", "tools/net461-x86/GetDocument.Insider.exe.config", "tools/net461-x86/Microsoft.Win32.Primitives.dll", "tools/net461-x86/System.AppContext.dll", "tools/net461-x86/System.Buffers.dll", "tools/net461-x86/System.Collections.Concurrent.dll", "tools/net461-x86/System.Collections.NonGeneric.dll", "tools/net461-x86/System.Collections.Specialized.dll", "tools/net461-x86/System.Collections.dll", "tools/net461-x86/System.ComponentModel.EventBasedAsync.dll", "tools/net461-x86/System.ComponentModel.Primitives.dll", "tools/net461-x86/System.ComponentModel.TypeConverter.dll", "tools/net461-x86/System.ComponentModel.dll", "tools/net461-x86/System.Console.dll", "tools/net461-x86/System.Data.Common.dll", "tools/net461-x86/System.Diagnostics.Contracts.dll", "tools/net461-x86/System.Diagnostics.Debug.dll", "tools/net461-x86/System.Diagnostics.DiagnosticSource.dll", "tools/net461-x86/System.Diagnostics.FileVersionInfo.dll", "tools/net461-x86/System.Diagnostics.Process.dll", "tools/net461-x86/System.Diagnostics.StackTrace.dll", "tools/net461-x86/System.Diagnostics.TextWriterTraceListener.dll", "tools/net461-x86/System.Diagnostics.Tools.dll", "tools/net461-x86/System.Diagnostics.TraceSource.dll", "tools/net461-x86/System.Diagnostics.Tracing.dll", "tools/net461-x86/System.Drawing.Primitives.dll", "tools/net461-x86/System.Dynamic.Runtime.dll", "tools/net461-x86/System.Globalization.Calendars.dll", "tools/net461-x86/System.Globalization.Extensions.dll", "tools/net461-x86/System.Globalization.dll", "tools/net461-x86/System.IO.Compression.ZipFile.dll", "tools/net461-x86/System.IO.Compression.dll", "tools/net461-x86/System.IO.FileSystem.DriveInfo.dll", "tools/net461-x86/System.IO.FileSystem.Primitives.dll", "tools/net461-x86/System.IO.FileSystem.Watcher.dll", "tools/net461-x86/System.IO.FileSystem.dll", "tools/net461-x86/System.IO.IsolatedStorage.dll", "tools/net461-x86/System.IO.MemoryMappedFiles.dll", "tools/net461-x86/System.IO.Pipes.dll", "tools/net461-x86/System.IO.UnmanagedMemoryStream.dll", "tools/net461-x86/System.IO.dll", "tools/net461-x86/System.Linq.Expressions.dll", "tools/net461-x86/System.Linq.Parallel.dll", "tools/net461-x86/System.Linq.Queryable.dll", "tools/net461-x86/System.Linq.dll", "tools/net461-x86/System.Memory.dll", "tools/net461-x86/System.Net.Http.dll", "tools/net461-x86/System.Net.NameResolution.dll", "tools/net461-x86/System.Net.NetworkInformation.dll", "tools/net461-x86/System.Net.Ping.dll", "tools/net461-x86/System.Net.Primitives.dll", "tools/net461-x86/System.Net.Requests.dll", "tools/net461-x86/System.Net.Security.dll", "tools/net461-x86/System.Net.Sockets.dll", "tools/net461-x86/System.Net.WebHeaderCollection.dll", "tools/net461-x86/System.Net.WebSockets.Client.dll", "tools/net461-x86/System.Net.WebSockets.dll", "tools/net461-x86/System.Numerics.Vectors.dll", "tools/net461-x86/System.ObjectModel.dll", "tools/net461-x86/System.Reflection.Extensions.dll", "tools/net461-x86/System.Reflection.Primitives.dll", "tools/net461-x86/System.Reflection.dll", "tools/net461-x86/System.Resources.Reader.dll", "tools/net461-x86/System.Resources.ResourceManager.dll", "tools/net461-x86/System.Resources.Writer.dll", "tools/net461-x86/System.Runtime.CompilerServices.Unsafe.dll", "tools/net461-x86/System.Runtime.CompilerServices.VisualC.dll", "tools/net461-x86/System.Runtime.Extensions.dll", "tools/net461-x86/System.Runtime.Handles.dll", "tools/net461-x86/System.Runtime.InteropServices.RuntimeInformation.dll", "tools/net461-x86/System.Runtime.InteropServices.dll", "tools/net461-x86/System.Runtime.Numerics.dll", "tools/net461-x86/System.Runtime.Serialization.Formatters.dll", "tools/net461-x86/System.Runtime.Serialization.Json.dll", "tools/net461-x86/System.Runtime.Serialization.Primitives.dll", "tools/net461-x86/System.Runtime.Serialization.Xml.dll", "tools/net461-x86/System.Runtime.dll", "tools/net461-x86/System.Security.Claims.dll", "tools/net461-x86/System.Security.Cryptography.Algorithms.dll", "tools/net461-x86/System.Security.Cryptography.Csp.dll", "tools/net461-x86/System.Security.Cryptography.Encoding.dll", "tools/net461-x86/System.Security.Cryptography.Primitives.dll", "tools/net461-x86/System.Security.Cryptography.X509Certificates.dll", "tools/net461-x86/System.Security.Principal.dll", "tools/net461-x86/System.Security.SecureString.dll", "tools/net461-x86/System.Text.Encoding.Extensions.dll", "tools/net461-x86/System.Text.Encoding.dll", "tools/net461-x86/System.Text.RegularExpressions.dll", "tools/net461-x86/System.Threading.Overlapped.dll", "tools/net461-x86/System.Threading.Tasks.Parallel.dll", "tools/net461-x86/System.Threading.Tasks.dll", "tools/net461-x86/System.Threading.Thread.dll", "tools/net461-x86/System.Threading.ThreadPool.dll", "tools/net461-x86/System.Threading.Timer.dll", "tools/net461-x86/System.Threading.dll", "tools/net461-x86/System.ValueTuple.dll", "tools/net461-x86/System.Xml.ReaderWriter.dll", "tools/net461-x86/System.Xml.XDocument.dll", "tools/net461-x86/System.Xml.XPath.XDocument.dll", "tools/net461-x86/System.Xml.XPath.dll", "tools/net461-x86/System.Xml.XmlDocument.dll", "tools/net461-x86/System.Xml.XmlSerializer.dll", "tools/net461-x86/netstandard.dll", "tools/net461/GetDocument.Insider.exe", "tools/net461/GetDocument.Insider.exe.config", "tools/net461/Microsoft.Win32.Primitives.dll", "tools/net461/System.AppContext.dll", "tools/net461/System.Buffers.dll", "tools/net461/System.Collections.Concurrent.dll", "tools/net461/System.Collections.NonGeneric.dll", "tools/net461/System.Collections.Specialized.dll", "tools/net461/System.Collections.dll", "tools/net461/System.ComponentModel.EventBasedAsync.dll", "tools/net461/System.ComponentModel.Primitives.dll", "tools/net461/System.ComponentModel.TypeConverter.dll", "tools/net461/System.ComponentModel.dll", "tools/net461/System.Console.dll", "tools/net461/System.Data.Common.dll", "tools/net461/System.Diagnostics.Contracts.dll", "tools/net461/System.Diagnostics.Debug.dll", "tools/net461/System.Diagnostics.DiagnosticSource.dll", "tools/net461/System.Diagnostics.FileVersionInfo.dll", "tools/net461/System.Diagnostics.Process.dll", "tools/net461/System.Diagnostics.StackTrace.dll", "tools/net461/System.Diagnostics.TextWriterTraceListener.dll", "tools/net461/System.Diagnostics.Tools.dll", "tools/net461/System.Diagnostics.TraceSource.dll", "tools/net461/System.Diagnostics.Tracing.dll", "tools/net461/System.Drawing.Primitives.dll", "tools/net461/System.Dynamic.Runtime.dll", "tools/net461/System.Globalization.Calendars.dll", "tools/net461/System.Globalization.Extensions.dll", "tools/net461/System.Globalization.dll", "tools/net461/System.IO.Compression.ZipFile.dll", "tools/net461/System.IO.Compression.dll", "tools/net461/System.IO.FileSystem.DriveInfo.dll", "tools/net461/System.IO.FileSystem.Primitives.dll", "tools/net461/System.IO.FileSystem.Watcher.dll", "tools/net461/System.IO.FileSystem.dll", "tools/net461/System.IO.IsolatedStorage.dll", "tools/net461/System.IO.MemoryMappedFiles.dll", "tools/net461/System.IO.Pipes.dll", "tools/net461/System.IO.UnmanagedMemoryStream.dll", "tools/net461/System.IO.dll", "tools/net461/System.Linq.Expressions.dll", "tools/net461/System.Linq.Parallel.dll", "tools/net461/System.Linq.Queryable.dll", "tools/net461/System.Linq.dll", "tools/net461/System.Memory.dll", "tools/net461/System.Net.Http.dll", "tools/net461/System.Net.NameResolution.dll", "tools/net461/System.Net.NetworkInformation.dll", "tools/net461/System.Net.Ping.dll", "tools/net461/System.Net.Primitives.dll", "tools/net461/System.Net.Requests.dll", "tools/net461/System.Net.Security.dll", "tools/net461/System.Net.Sockets.dll", "tools/net461/System.Net.WebHeaderCollection.dll", "tools/net461/System.Net.WebSockets.Client.dll", "tools/net461/System.Net.WebSockets.dll", "tools/net461/System.Numerics.Vectors.dll", "tools/net461/System.ObjectModel.dll", "tools/net461/System.Reflection.Extensions.dll", "tools/net461/System.Reflection.Primitives.dll", "tools/net461/System.Reflection.dll", "tools/net461/System.Resources.Reader.dll", "tools/net461/System.Resources.ResourceManager.dll", "tools/net461/System.Resources.Writer.dll", "tools/net461/System.Runtime.CompilerServices.Unsafe.dll", "tools/net461/System.Runtime.CompilerServices.VisualC.dll", "tools/net461/System.Runtime.Extensions.dll", "tools/net461/System.Runtime.Handles.dll", "tools/net461/System.Runtime.InteropServices.RuntimeInformation.dll", "tools/net461/System.Runtime.InteropServices.dll", "tools/net461/System.Runtime.Numerics.dll", "tools/net461/System.Runtime.Serialization.Formatters.dll", "tools/net461/System.Runtime.Serialization.Json.dll", "tools/net461/System.Runtime.Serialization.Primitives.dll", "tools/net461/System.Runtime.Serialization.Xml.dll", "tools/net461/System.Runtime.dll", "tools/net461/System.Security.Claims.dll", "tools/net461/System.Security.Cryptography.Algorithms.dll", "tools/net461/System.Security.Cryptography.Csp.dll", "tools/net461/System.Security.Cryptography.Encoding.dll", "tools/net461/System.Security.Cryptography.Primitives.dll", "tools/net461/System.Security.Cryptography.X509Certificates.dll", "tools/net461/System.Security.Principal.dll", "tools/net461/System.Security.SecureString.dll", "tools/net461/System.Text.Encoding.Extensions.dll", "tools/net461/System.Text.Encoding.dll", "tools/net461/System.Text.RegularExpressions.dll", "tools/net461/System.Threading.Overlapped.dll", "tools/net461/System.Threading.Tasks.Parallel.dll", "tools/net461/System.Threading.Tasks.dll", "tools/net461/System.Threading.Thread.dll", "tools/net461/System.Threading.ThreadPool.dll", "tools/net461/System.Threading.Timer.dll", "tools/net461/System.Threading.dll", "tools/net461/System.ValueTuple.dll", "tools/net461/System.Xml.ReaderWriter.dll", "tools/net461/System.Xml.XDocument.dll", "tools/net461/System.Xml.XPath.XDocument.dll", "tools/net461/System.Xml.XPath.dll", "tools/net461/System.Xml.XmlDocument.dll", "tools/net461/System.Xml.XmlSerializer.dll", "tools/net461/netstandard.dll", "tools/netcoreapp2.1/GetDocument.Insider.deps.json", "tools/netcoreapp2.1/GetDocument.Insider.dll", "tools/netcoreapp2.1/GetDocument.Insider.runtimeconfig.json", "tools/netcoreapp2.1/System.Diagnostics.DiagnosticSource.dll"]}, "Microsoft.Extensions.Caching.Abstractions/9.0.5": {"sha512": "RV6wOTvH5BeVRs6cvxFuaV1ut05Dklpvq19XRO1JxAayfLWYIEP7K94aamY0iSUhoehWk1X5H6gMcbZkHuBjew==", "type": "package", "path": "microsoft.extensions.caching.abstractions/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Abstractions.targets", "lib/net462/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net462/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.xml", "microsoft.extensions.caching.abstractions.9.0.5.nupkg.sha512", "microsoft.extensions.caching.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Caching.Memory/9.0.5": {"sha512": "qDmoAzIUBup5KZG1Abv51ifbHMCWFnaXbt05l+Sd92mLOpF9OwHOuoxu3XhzXaPGfq0Ns3pv1df5l8zuKjFgGw==", "type": "package", "path": "microsoft.extensions.caching.memory/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Memory.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Memory.targets", "lib/net462/Microsoft.Extensions.Caching.Memory.dll", "lib/net462/Microsoft.Extensions.Caching.Memory.xml", "lib/net8.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net8.0/Microsoft.Extensions.Caching.Memory.xml", "lib/net9.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net9.0/Microsoft.Extensions.Caching.Memory.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.xml", "microsoft.extensions.caching.memory.9.0.5.nupkg.sha512", "microsoft.extensions.caching.memory.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration/9.0.1": {"sha512": "VuthqFS+ju6vT8W4wevdhEFiRi1trvQtkzWLonApfF5USVzzDcTBoY3F24WvN/tffLSrycArVfX1bThm/9xY2A==", "type": "package", "path": "microsoft.extensions.configuration/9.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.targets", "lib/net462/Microsoft.Extensions.Configuration.dll", "lib/net462/Microsoft.Extensions.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Configuration.xml", "lib/net9.0/Microsoft.Extensions.Configuration.dll", "lib/net9.0/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.9.0.1.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/9.0.5": {"sha512": "ew0G6gIznnyAkbIa67wXspkDFcVektjN3xaDAfBDIPbWph+rbuGaaohFxUSGw28ht7wdcWtTtElKnzfkcDDbOQ==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.9.0.5.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.1": {"sha512": "QBOI8YVAyKqeshYOyxSe6co22oag431vxMu5xQe1EjXMkYE4xK4J71xLCW3/bWKmr9Aoy1VqGUARSLFnotk4Bg==", "type": "package", "path": "microsoft.extensions.configuration.fileextensions/9.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.FileExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.FileExtensions.targets", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "microsoft.extensions.configuration.fileextensions.9.0.1.nupkg.sha512", "microsoft.extensions.configuration.fileextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Json/9.0.1": {"sha512": "z+g+lgPET1JRDjsOkFe51rkkNcnJgvOK5UIpeTfF1iAi0GkBJz5/yUuTa8a9V8HUh4gj4xFT5WGoMoXoSDKfGg==", "type": "package", "path": "microsoft.extensions.configuration.json/9.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Json.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Json.targets", "lib/net462/Microsoft.Extensions.Configuration.Json.dll", "lib/net462/Microsoft.Extensions.Configuration.Json.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.xml", "microsoft.extensions.configuration.json.9.0.1.nupkg.sha512", "microsoft.extensions.configuration.json.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/9.0.5": {"sha512": "N1Mn0T/tUBPoLL+Fzsp+VCEtneUhhxc1//Dx3BeuQ8AX+XrMlYCfnp2zgpEXnTCB7053CLdiqVWPZ7mEX6MPjg==", "type": "package", "path": "microsoft.extensions.dependencyinjection/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.9.0.5.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.5": {"sha512": "cjnRtsEAzU73aN6W7vkWy8Phj5t3Xm78HSqgrbh/O4Q9SK/yN73wZVa21QQY6amSLQRQ/M8N+koGnY6PuvKQsw==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.9.0.5.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyModel/9.0.5": {"sha512": "+jdJ9Vz+5Ia21l3KjahtmeHCIgQ7urfkdcJPxSfeqB40Jqryi27Lt4fKBmKyvc0YrfTUJ0cEB7QmoQRlU8FH0g==", "type": "package", "path": "microsoft.extensions.dependencymodel/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyModel.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyModel.targets", "lib/net462/Microsoft.Extensions.DependencyModel.dll", "lib/net462/Microsoft.Extensions.DependencyModel.xml", "lib/net8.0/Microsoft.Extensions.DependencyModel.dll", "lib/net8.0/Microsoft.Extensions.DependencyModel.xml", "lib/net9.0/Microsoft.Extensions.DependencyModel.dll", "lib/net9.0/Microsoft.Extensions.DependencyModel.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.xml", "microsoft.extensions.dependencymodel.9.0.5.nupkg.sha512", "microsoft.extensions.dependencymodel.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Features/8.0.10": {"sha512": "6SpN2/BuqUnhrw1i+vXsw7CA4ADYt7lf1G9/eDs+bY7eJoug5YQVFd4OS+37m8dSbklCdq6b7rLbCVQUZgL6oQ==", "type": "package", "path": "microsoft.extensions.features/8.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.Extensions.Features.dll", "lib/net462/Microsoft.Extensions.Features.xml", "lib/net8.0/Microsoft.Extensions.Features.dll", "lib/net8.0/Microsoft.Extensions.Features.xml", "lib/netstandard2.0/Microsoft.Extensions.Features.dll", "lib/netstandard2.0/Microsoft.Extensions.Features.xml", "microsoft.extensions.features.8.0.10.nupkg.sha512", "microsoft.extensions.features.nuspec"]}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.1": {"sha512": "DguZYt1DWL05+8QKWL3b6bW7A2pC5kYFMY5iXM6W2M23jhvcNa8v6AU8PvVJBcysxHwr9/jax0agnwoBumsSwg==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/9.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Abstractions.targets", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.9.0.1.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Physical/9.0.1": {"sha512": "TKDMNRS66UTMEVT38/tU9hA63UTMvzI3DyNm5mx8+JCf3BaOtxgrvWLCI1y3J52PzT5yNl/T2KN5Z0KbApLZcg==", "type": "package", "path": "microsoft.extensions.fileproviders.physical/9.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Physical.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Physical.targets", "lib/net462/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net462/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net9.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.xml", "microsoft.extensions.fileproviders.physical.9.0.1.nupkg.sha512", "microsoft.extensions.fileproviders.physical.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileSystemGlobbing/9.0.1": {"sha512": "Mxcp9NXuQMvAnudRZcgIb5SqlWrlullQzntBLTwuv0MPIJ5LqiGwbRqiyxgdk+vtCoUkplb0oXy5kAw1t469Ug==", "type": "package", "path": "microsoft.extensions.filesystemglobbing/9.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileSystemGlobbing.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileSystemGlobbing.targets", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.xml", "microsoft.extensions.filesystemglobbing.9.0.1.nupkg.sha512", "microsoft.extensions.filesystemglobbing.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Identity.Core/8.0.10": {"sha512": "tS0lNRccAxuAeIVxLBDdklSOL2vAzVUcYqY0njsRbJpNYrXNIKVeQGmhPJgBU0Vrq+iu0LLJ4KLCqGxsOIWpyw==", "type": "package", "path": "microsoft.extensions.identity.core/8.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.Extensions.Identity.Core.dll", "lib/net462/Microsoft.Extensions.Identity.Core.xml", "lib/net8.0/Microsoft.Extensions.Identity.Core.dll", "lib/net8.0/Microsoft.Extensions.Identity.Core.xml", "lib/netstandard2.0/Microsoft.Extensions.Identity.Core.dll", "lib/netstandard2.0/Microsoft.Extensions.Identity.Core.xml", "microsoft.extensions.identity.core.8.0.10.nupkg.sha512", "microsoft.extensions.identity.core.nuspec"]}, "Microsoft.Extensions.Identity.Stores/8.0.10": {"sha512": "Mwxhj2pLwFcT8BOJ4g7y/WQyQSmZNOalIHmyISFlWykPEKgaQXOlddOCOftSIUqh4IZEYDsVXjeecjl9RLC8Lw==", "type": "package", "path": "microsoft.extensions.identity.stores/8.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.Extensions.Identity.Stores.dll", "lib/net462/Microsoft.Extensions.Identity.Stores.xml", "lib/net8.0/Microsoft.Extensions.Identity.Stores.dll", "lib/net8.0/Microsoft.Extensions.Identity.Stores.xml", "lib/netstandard2.0/Microsoft.Extensions.Identity.Stores.dll", "lib/netstandard2.0/Microsoft.Extensions.Identity.Stores.xml", "microsoft.extensions.identity.stores.8.0.10.nupkg.sha512", "microsoft.extensions.identity.stores.nuspec"]}, "Microsoft.Extensions.Logging/9.0.5": {"sha512": "rQU61lrgvpE/UgcAd4E56HPxUIkX/VUQCxWmwDTLLVeuwRDYTL0q/FLGfAW17cGTKyCh7ywYAEnY3sTEvURsfg==", "type": "package", "path": "microsoft.extensions.logging/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net8.0/Microsoft.Extensions.Logging.dll", "lib/net8.0/Microsoft.Extensions.Logging.xml", "lib/net9.0/Microsoft.Extensions.Logging.dll", "lib/net9.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.9.0.5.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/9.0.5": {"sha512": "pP1PADCrIxMYJXxFmTVbAgEU7GVpjK5i0/tyfU9DiE0oXQy3JWQaOVgCkrCiePLgS8b5sghM3Fau3EeHiVWbCg==", "type": "package", "path": "microsoft.extensions.logging.abstractions/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.9.0.5.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options/9.0.5": {"sha512": "vPdJQU8YLOUSSK8NL0RmwcXJr2E0w8xH559PGQl4JYsglgilZr9LZnqV2zdgk+XR05+kuvhBEZKoDVd46o7NqA==", "type": "package", "path": "microsoft.extensions.options/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net8.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/net9.0/Microsoft.Extensions.Options.dll", "lib/net9.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.9.0.5.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/9.0.5": {"sha512": "b4OAv1qE1C9aM+ShWJu3rlo/WjDwa/I30aIPXqDWSKXTtKl1Wwh6BZn+glH5HndGVVn3C6ZAPQj5nv7/7HJNBQ==", "type": "package", "path": "microsoft.extensions.primitives/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/net9.0/Microsoft.Extensions.Primitives.dll", "lib/net9.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.9.0.5.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Identity.Abstractions/7.1.0": {"sha512": "/lycPG0hdzmlD1MEpkzYQz5uoveO0pTDc32wKlVQ1c0GB1kLqxA7EV5XsSQp6VPTn/7KOa0crwidp15VLZs3eQ==", "type": "package", "path": "microsoft.identity.abstractions/7.1.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "README.md", "lib/net462/Microsoft.Identity.Abstractions.dll", "lib/net462/Microsoft.Identity.Abstractions.xml", "lib/net8.0/Microsoft.Identity.Abstractions.dll", "lib/net8.0/Microsoft.Identity.Abstractions.xml", "lib/netstandard2.0/Microsoft.Identity.Abstractions.dll", "lib/netstandard2.0/Microsoft.Identity.Abstractions.xml", "lib/netstandard2.1/Microsoft.Identity.Abstractions.dll", "lib/netstandard2.1/Microsoft.Identity.Abstractions.xml", "microsoft.identity.abstractions.7.1.0.nupkg.sha512", "microsoft.identity.abstractions.nuspec"]}, "Microsoft.Identity.Client/4.65.0": {"sha512": "RV35ZcJ5/P7n+Zu6J3wmtiDdK6MW5h6EpZ0ojjB9sMwNhGHEJCv829cb3kZ4PZ664llYFv8sbUITWWGvBTqv0Q==", "type": "package", "path": "microsoft.identity.client/4.65.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.Identity.Client.dll", "lib/net462/Microsoft.Identity.Client.xml", "lib/net472/Microsoft.Identity.Client.dll", "lib/net472/Microsoft.Identity.Client.xml", "lib/net6.0-android31.0/Microsoft.Identity.Client.dll", "lib/net6.0-android31.0/Microsoft.Identity.Client.xml", "lib/net6.0-ios15.4/Microsoft.Identity.Client.dll", "lib/net6.0-ios15.4/Microsoft.Identity.Client.xml", "lib/net6.0/Microsoft.Identity.Client.dll", "lib/net6.0/Microsoft.Identity.Client.xml", "lib/netstandard2.0/Microsoft.Identity.Client.dll", "lib/netstandard2.0/Microsoft.Identity.Client.xml", "microsoft.identity.client.4.65.0.nupkg.sha512", "microsoft.identity.client.nuspec"]}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"sha512": "PWnJcznrSGr25MN8ajlc2XIDW4zCFu0U6FkpaNLEWLgd1NgFCp5uDY3mqLDgM8zCN8hqj8yo5wHYfLB2HjcdGw==", "type": "package", "path": "microsoft.identity.client.extensions.msal/4.61.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll", "lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.xml", "lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.dll", "lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.xml", "microsoft.identity.client.extensions.msal.4.61.3.nupkg.sha512", "microsoft.identity.client.extensions.msal.nuspec"]}, "Microsoft.Identity.Web/3.2.2": {"sha512": "N75Gyb0RAxydepwVEhKTDS4Db/GO2pYsSeMvqmQizIKdmQQmCFYMeHdtSTiEsv/bng9zmNOmwarz/d7ONnTMuQ==", "type": "package", "path": "microsoft.identity.web/3.2.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "README.md", "lib/net462/Microsoft.Identity.Web.dll", "lib/net462/Microsoft.Identity.Web.xml", "lib/net472/Microsoft.Identity.Web.dll", "lib/net472/Microsoft.Identity.Web.xml", "lib/net6.0/Microsoft.Identity.Web.dll", "lib/net6.0/Microsoft.Identity.Web.xml", "lib/net7.0/Microsoft.Identity.Web.dll", "lib/net7.0/Microsoft.Identity.Web.xml", "lib/net8.0/Microsoft.Identity.Web.dll", "lib/net8.0/Microsoft.Identity.Web.xml", "lib/net9.0/Microsoft.Identity.Web.dll", "lib/net9.0/Microsoft.Identity.Web.xml", "lib/netstandard2.0/Microsoft.Identity.Web.dll", "lib/netstandard2.0/Microsoft.Identity.Web.xml", "microsoft.identity.web.3.2.2.nupkg.sha512", "microsoft.identity.web.nuspec"]}, "Microsoft.Identity.Web.Certificate/3.2.2": {"sha512": "Xe1xAvGx9ZLy0wLEsmbsSCpIVjWzhsUAl5EN7RWqd+zeb2fEOqRInCxgS9eLCAD4wfrcATzHt3ELHtB5to/b8w==", "type": "package", "path": "microsoft.identity.web.certificate/3.2.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "README.md", "lib/net462/Microsoft.Identity.Web.Certificate.dll", "lib/net462/Microsoft.Identity.Web.Certificate.xml", "lib/net472/Microsoft.Identity.Web.Certificate.dll", "lib/net472/Microsoft.Identity.Web.Certificate.xml", "lib/net6.0/Microsoft.Identity.Web.Certificate.dll", "lib/net6.0/Microsoft.Identity.Web.Certificate.xml", "lib/net7.0/Microsoft.Identity.Web.Certificate.dll", "lib/net7.0/Microsoft.Identity.Web.Certificate.xml", "lib/net8.0/Microsoft.Identity.Web.Certificate.dll", "lib/net8.0/Microsoft.Identity.Web.Certificate.xml", "lib/net9.0/Microsoft.Identity.Web.Certificate.dll", "lib/net9.0/Microsoft.Identity.Web.Certificate.xml", "lib/netstandard2.0/Microsoft.Identity.Web.Certificate.dll", "lib/netstandard2.0/Microsoft.Identity.Web.Certificate.xml", "microsoft.identity.web.certificate.3.2.2.nupkg.sha512", "microsoft.identity.web.certificate.nuspec"]}, "Microsoft.Identity.Web.Certificateless/3.2.2": {"sha512": "U/liNxeAitSQ/zmSf9W93PdWvXxIVqsrPF0qGY80dsRQy8yu7SxB2X1a7lgzTOqn2BtczemGLyj1QdP/86wNAQ==", "type": "package", "path": "microsoft.identity.web.certificateless/3.2.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "README.md", "lib/netstandard2.0/Microsoft.Identity.Web.Certificateless.dll", "lib/netstandard2.0/Microsoft.Identity.Web.Certificateless.xml", "microsoft.identity.web.certificateless.3.2.2.nupkg.sha512", "microsoft.identity.web.certificateless.nuspec"]}, "Microsoft.Identity.Web.Diagnostics/3.2.2": {"sha512": "ms4/yJzRuZ7Qp0lH73tRWz2XnjHopHxrZv0VgJXrqB1HvTP1uCnoq/DM+qs8XVwG2lvejMFplsv5vU3l2xqOOg==", "type": "package", "path": "microsoft.identity.web.diagnostics/3.2.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "README.md", "lib/net462/Microsoft.Identity.Web.Diagnostics.dll", "lib/net462/Microsoft.Identity.Web.Diagnostics.xml", "lib/net472/Microsoft.Identity.Web.Diagnostics.dll", "lib/net472/Microsoft.Identity.Web.Diagnostics.xml", "lib/net6.0/Microsoft.Identity.Web.Diagnostics.dll", "lib/net6.0/Microsoft.Identity.Web.Diagnostics.xml", "lib/net7.0/Microsoft.Identity.Web.Diagnostics.dll", "lib/net7.0/Microsoft.Identity.Web.Diagnostics.xml", "lib/net8.0/Microsoft.Identity.Web.Diagnostics.dll", "lib/net8.0/Microsoft.Identity.Web.Diagnostics.xml", "lib/net9.0/Microsoft.Identity.Web.Diagnostics.dll", "lib/net9.0/Microsoft.Identity.Web.Diagnostics.xml", "lib/netstandard2.0/Microsoft.Identity.Web.Diagnostics.dll", "lib/netstandard2.0/Microsoft.Identity.Web.Diagnostics.xml", "microsoft.identity.web.diagnostics.3.2.2.nupkg.sha512", "microsoft.identity.web.diagnostics.nuspec"]}, "Microsoft.Identity.Web.TokenAcquisition/3.2.2": {"sha512": "WWqmmG9nE3C+yzduvh8ieb7Hti26ltfGKy9BtzpMoqLTc/VKbHxFl3d9elRztmNeukjUvOmKvtqbykFkeTtCqQ==", "type": "package", "path": "microsoft.identity.web.tokenacquisition/3.2.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "README.md", "lib/net462/Microsoft.Identity.Web.TokenAcquisition.dll", "lib/net462/Microsoft.Identity.Web.TokenAcquisition.xml", "lib/net472/Microsoft.Identity.Web.TokenAcquisition.dll", "lib/net472/Microsoft.Identity.Web.TokenAcquisition.xml", "lib/net6.0/Microsoft.Identity.Web.TokenAcquisition.dll", "lib/net6.0/Microsoft.Identity.Web.TokenAcquisition.xml", "lib/net7.0/Microsoft.Identity.Web.TokenAcquisition.dll", "lib/net7.0/Microsoft.Identity.Web.TokenAcquisition.xml", "lib/net8.0/Microsoft.Identity.Web.TokenAcquisition.dll", "lib/net8.0/Microsoft.Identity.Web.TokenAcquisition.xml", "lib/net9.0/Microsoft.Identity.Web.TokenAcquisition.dll", "lib/net9.0/Microsoft.Identity.Web.TokenAcquisition.xml", "lib/netstandard2.0/Microsoft.Identity.Web.TokenAcquisition.dll", "lib/netstandard2.0/Microsoft.Identity.Web.TokenAcquisition.xml", "microsoft.identity.web.tokenacquisition.3.2.2.nupkg.sha512", "microsoft.identity.web.tokenacquisition.nuspec"]}, "Microsoft.Identity.Web.TokenCache/3.2.2": {"sha512": "IdblF2ySKDuv12hP/WT1LPovuVOqYepV6UYzABDaJQaFnA8C4fFAV+ez8yp7rT7Mf3iDtRO0W66ESSnlb0NdQQ==", "type": "package", "path": "microsoft.identity.web.tokencache/3.2.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "README.md", "lib/net462/Microsoft.Identity.Web.TokenCache.dll", "lib/net462/Microsoft.Identity.Web.TokenCache.xml", "lib/net472/Microsoft.Identity.Web.TokenCache.dll", "lib/net472/Microsoft.Identity.Web.TokenCache.xml", "lib/net6.0/Microsoft.Identity.Web.TokenCache.dll", "lib/net6.0/Microsoft.Identity.Web.TokenCache.xml", "lib/net7.0/Microsoft.Identity.Web.TokenCache.dll", "lib/net7.0/Microsoft.Identity.Web.TokenCache.xml", "lib/net8.0/Microsoft.Identity.Web.TokenCache.dll", "lib/net8.0/Microsoft.Identity.Web.TokenCache.xml", "lib/net9.0/Microsoft.Identity.Web.TokenCache.dll", "lib/net9.0/Microsoft.Identity.Web.TokenCache.xml", "lib/netstandard2.0/Microsoft.Identity.Web.TokenCache.dll", "lib/netstandard2.0/Microsoft.Identity.Web.TokenCache.xml", "microsoft.identity.web.tokencache.3.2.2.nupkg.sha512", "microsoft.identity.web.tokencache.nuspec"]}, "Microsoft.IdentityModel.Abstractions/8.1.2": {"sha512": "QSSDer3kvyTdNq6BefgX4EYi1lsia2zJUh5CfIMZFQUh6BhrXK1WE4i2C9ltUmmuUjoeVVX6AaSo9NZfpTGNdw==", "type": "package", "path": "microsoft.identitymodel.abstractions/8.1.2", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.IdentityModel.Abstractions.dll", "lib/net462/Microsoft.IdentityModel.Abstractions.xml", "lib/net472/Microsoft.IdentityModel.Abstractions.dll", "lib/net472/Microsoft.IdentityModel.Abstractions.xml", "lib/net6.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net6.0/Microsoft.IdentityModel.Abstractions.xml", "lib/net8.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net8.0/Microsoft.IdentityModel.Abstractions.xml", "lib/net9.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net9.0/Microsoft.IdentityModel.Abstractions.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.xml", "microsoft.identitymodel.abstractions.8.1.2.nupkg.sha512", "microsoft.identitymodel.abstractions.nuspec"]}, "Microsoft.IdentityModel.JsonWebTokens/8.1.2": {"sha512": "AWQINMvtamdYBqtG8q8muyYTfA9i5xRBEsMKQdzOn5xRzhVVDSzsNGYof1docfF3pX4hNRUpHlzs61RP0reZMw==", "type": "package", "path": "microsoft.identitymodel.jsonwebtokens/8.1.2", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net462/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.xml", "microsoft.identitymodel.jsonwebtokens.8.1.2.nupkg.sha512", "microsoft.identitymodel.jsonwebtokens.nuspec"]}, "Microsoft.IdentityModel.Logging/8.1.2": {"sha512": "pEn//qKJcEXDsLHLzACFrT3a2kkpIGOXLEYkcuxjqWoeDnbeotu0LY9fF8+Ds9WWpVE9ZGlxXamT0VR8rxaQeA==", "type": "package", "path": "microsoft.identitymodel.logging/8.1.2", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.IdentityModel.Logging.dll", "lib/net462/Microsoft.IdentityModel.Logging.xml", "lib/net472/Microsoft.IdentityModel.Logging.dll", "lib/net472/Microsoft.IdentityModel.Logging.xml", "lib/net6.0/Microsoft.IdentityModel.Logging.dll", "lib/net6.0/Microsoft.IdentityModel.Logging.xml", "lib/net8.0/Microsoft.IdentityModel.Logging.dll", "lib/net8.0/Microsoft.IdentityModel.Logging.xml", "lib/net9.0/Microsoft.IdentityModel.Logging.dll", "lib/net9.0/Microsoft.IdentityModel.Logging.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.xml", "microsoft.identitymodel.logging.8.1.2.nupkg.sha512", "microsoft.identitymodel.logging.nuspec"]}, "Microsoft.IdentityModel.LoggingExtensions/8.1.2": {"sha512": "eS/IBBE19VkUL1DtoIhTOO8X0DyYJ1nWEJ9kpxua2tkjnwvn/H3GZBYOoev0B1bB1rcRv+neYEpA6dyhOR9fxQ==", "type": "package", "path": "microsoft.identitymodel.loggingextensions/8.1.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.IdentityModel.LoggingExtensions.dll", "lib/netstandard2.0/Microsoft.IdentityModel.LoggingExtensions.xml", "microsoft.identitymodel.loggingextensions.8.1.2.nupkg.sha512", "microsoft.identitymodel.loggingextensions.nuspec"]}, "Microsoft.IdentityModel.Protocols/8.1.2": {"sha512": "Yu3UJWIFX2/5m2MZskECqByr62L8A0uTtTblWIxy0wJNUg0OJGhIK6oRdpcZ8xbSJYD/SOE8psjo5IXRqC3Bsw==", "type": "package", "path": "microsoft.identitymodel.protocols/8.1.2", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.IdentityModel.Protocols.dll", "lib/net462/Microsoft.IdentityModel.Protocols.xml", "lib/net472/Microsoft.IdentityModel.Protocols.dll", "lib/net472/Microsoft.IdentityModel.Protocols.xml", "lib/net6.0/Microsoft.IdentityModel.Protocols.dll", "lib/net6.0/Microsoft.IdentityModel.Protocols.xml", "lib/net8.0/Microsoft.IdentityModel.Protocols.dll", "lib/net8.0/Microsoft.IdentityModel.Protocols.xml", "lib/net9.0/Microsoft.IdentityModel.Protocols.dll", "lib/net9.0/Microsoft.IdentityModel.Protocols.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.xml", "microsoft.identitymodel.protocols.8.1.2.nupkg.sha512", "microsoft.identitymodel.protocols.nuspec"]}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.1.2": {"sha512": "eEtnzZiYymJYaguYeIXyviUocltBQzeYI0bEtot1Nrnl+qklCZARgk+SAaeYfdmc9CYo7aqP5UJ78rTTSTpQGQ==", "type": "package", "path": "microsoft.identitymodel.protocols.openidconnect/8.1.2", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net462/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net9.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net9.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "microsoft.identitymodel.protocols.openidconnect.8.1.2.nupkg.sha512", "microsoft.identitymodel.protocols.openidconnect.nuspec"]}, "Microsoft.IdentityModel.Tokens/8.1.2": {"sha512": "ZSzGsAA3BY20XHnsp8OjrHFtpd+pQtiu4UJDjPtXwCtEzcE5CjWP/8iZEJXy5AxVEFB0z6EwLSN+T1Fsdpjifw==", "type": "package", "path": "microsoft.identitymodel.tokens/8.1.2", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.IdentityModel.Tokens.dll", "lib/net462/Microsoft.IdentityModel.Tokens.xml", "lib/net472/Microsoft.IdentityModel.Tokens.dll", "lib/net472/Microsoft.IdentityModel.Tokens.xml", "lib/net6.0/Microsoft.IdentityModel.Tokens.dll", "lib/net6.0/Microsoft.IdentityModel.Tokens.xml", "lib/net8.0/Microsoft.IdentityModel.Tokens.dll", "lib/net8.0/Microsoft.IdentityModel.Tokens.xml", "lib/net9.0/Microsoft.IdentityModel.Tokens.dll", "lib/net9.0/Microsoft.IdentityModel.Tokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.xml", "microsoft.identitymodel.tokens.8.1.2.nupkg.sha512", "microsoft.identitymodel.tokens.nuspec"]}, "Microsoft.IdentityModel.Validators/8.1.2": {"sha512": "gC4VQ1CGBFlPbx8T6kMLSuzs9SPTpK53L+oT+cbIVvwJFPorw/kvgfwvASGN2BN1Rh8naz5wfVXSKm25LpYlKQ==", "type": "package", "path": "microsoft.identitymodel.validators/8.1.2", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.IdentityModel.Validators.dll", "lib/net462/Microsoft.IdentityModel.Validators.xml", "lib/net472/Microsoft.IdentityModel.Validators.dll", "lib/net472/Microsoft.IdentityModel.Validators.xml", "lib/net6.0/Microsoft.IdentityModel.Validators.dll", "lib/net6.0/Microsoft.IdentityModel.Validators.xml", "lib/net8.0/Microsoft.IdentityModel.Validators.dll", "lib/net8.0/Microsoft.IdentityModel.Validators.xml", "lib/net9.0/Microsoft.IdentityModel.Validators.dll", "lib/net9.0/Microsoft.IdentityModel.Validators.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Validators.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Validators.xml", "microsoft.identitymodel.validators.8.1.2.nupkg.sha512", "microsoft.identitymodel.validators.nuspec"]}, "Microsoft.IO.RecyclableMemoryStream/3.0.1": {"sha512": "s/s20YTVY9r9TPfTrN5g8zPF1YhwxyqO6PxUkrYTGI2B+OGPe9AdajWZrLhFqXIvqIW23fnUE4+ztrUWNU1+9g==", "type": "package", "path": "microsoft.io.recyclablememorystream/3.0.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll", "lib/net6.0/Microsoft.IO.RecyclableMemoryStream.xml", "lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.dll", "lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.xml", "lib/netstandard2.1/Microsoft.IO.RecyclableMemoryStream.dll", "lib/netstandard2.1/Microsoft.IO.RecyclableMemoryStream.xml", "microsoft.io.recyclablememorystream.3.0.1.nupkg.sha512", "microsoft.io.recyclablememorystream.nuspec"]}, "Microsoft.NETCore.Platforms/1.1.0": {"sha512": "kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "type": "package", "path": "microsoft.netcore.platforms/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.1.1.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json"]}, "Microsoft.OpenApi/1.6.23": {"sha512": "tZ1I0KXnn98CWuV8cpI247A17jaY+ILS9vvF7yhI0uPPEqF4P1d7BWL5Uwtel10w9NucllHB3nTkfYTAcHAh8g==", "type": "package", "path": "microsoft.openapi/1.6.23", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/netstandard2.0/Microsoft.OpenApi.dll", "lib/netstandard2.0/Microsoft.OpenApi.pdb", "lib/netstandard2.0/Microsoft.OpenApi.xml", "microsoft.openapi.1.6.23.nupkg.sha512", "microsoft.openapi.nuspec"]}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets/1.21.0": {"sha512": "8NudeHOE56YsY59HYY89akRMup8Ho+7Y3cADTGjajjWroXVU9RQai2nA6PfteB8AuzmRHZ5NZQB2BnWhQEul5g==", "type": "package", "path": "microsoft.visualstudio.azure.containers.tools.targets/1.21.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "EULA.md", "ThirdPartyNotices.txt", "build/Container.props", "build/Container.targets", "build/Microsoft.VisualStudio.Azure.Containers.Tools.Targets.props", "build/Microsoft.VisualStudio.Azure.Containers.Tools.Targets.targets", "build/Rules/GeneralBrowseObject.xaml", "build/Rules/cs-CZ/GeneralBrowseObject.xaml", "build/Rules/de-DE/GeneralBrowseObject.xaml", "build/Rules/es-ES/GeneralBrowseObject.xaml", "build/Rules/fr-FR/GeneralBrowseObject.xaml", "build/Rules/it-IT/GeneralBrowseObject.xaml", "build/Rules/ja-JP/GeneralBrowseObject.xaml", "build/Rules/ko-KR/GeneralBrowseObject.xaml", "build/Rules/pl-PL/GeneralBrowseObject.xaml", "build/Rules/pt-BR/GeneralBrowseObject.xaml", "build/Rules/ru-RU/GeneralBrowseObject.xaml", "build/Rules/tr-TR/GeneralBrowseObject.xaml", "build/Rules/zh-CN/GeneralBrowseObject.xaml", "build/Rules/zh-TW/GeneralBrowseObject.xaml", "build/ToolsTarget.props", "build/ToolsTarget.targets", "icon.png", "microsoft.visualstudio.azure.containers.tools.targets.1.21.0.nupkg.sha512", "microsoft.visualstudio.azure.containers.tools.targets.nuspec", "tools/Microsoft.VisualStudio.Containers.Tools.Common.dll", "tools/Microsoft.VisualStudio.Containers.Tools.Shared.dll", "tools/Microsoft.VisualStudio.Containers.Tools.Tasks.dll", "tools/Newtonsoft.Json.dll", "tools/System.Security.Principal.Windows.dll", "tools/cs/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/cs/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/cs/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/de/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/de/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/de/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/es/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/es/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/es/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/fr/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/fr/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/fr/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/it/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/it/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/it/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/ja/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/ja/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/ja/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/ko/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/ko/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/ko/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/pl/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/pl/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/pl/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/pt-BR/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/pt-BR/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/pt-BR/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/ru/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/ru/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/ru/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/tr/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/tr/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/tr/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/zh-Hans/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/zh-Hans/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/zh-Hans/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/zh-Hant/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/zh-Hant/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/zh-Hant/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll"]}, "Microsoft.Win32.SystemEvents/8.0.0": {"sha512": "9opKRyOKMCi2xJ7Bj7kxtZ1r9vbzosMvRrdEhVhDz8j8MoBGgB+WmC94yH839NPH+BclAjtQ/pyagvi/8gDLkw==", "type": "package", "path": "microsoft.win32.systemevents/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Win32.SystemEvents.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Win32.SystemEvents.targets", "lib/net462/Microsoft.Win32.SystemEvents.dll", "lib/net462/Microsoft.Win32.SystemEvents.xml", "lib/net6.0/Microsoft.Win32.SystemEvents.dll", "lib/net6.0/Microsoft.Win32.SystemEvents.xml", "lib/net7.0/Microsoft.Win32.SystemEvents.dll", "lib/net7.0/Microsoft.Win32.SystemEvents.xml", "lib/net8.0/Microsoft.Win32.SystemEvents.dll", "lib/net8.0/Microsoft.Win32.SystemEvents.xml", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "microsoft.win32.systemevents.8.0.0.nupkg.sha512", "microsoft.win32.systemevents.nuspec", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.xml", "useSharedDesignerContext.txt"]}, "Mono.TextTemplating/3.0.0": {"sha512": "YqueG52R/Xej4VVbKuRIodjiAhV0HR/XVbLbNrJhCZnzjnSjgMJ/dCdV0akQQxavX6hp/LC6rqLGLcXeQYU7XA==", "type": "package", "path": "mono.texttemplating/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt/LICENSE", "buildTransitive/Mono.TextTemplating.targets", "lib/net472/Mono.TextTemplating.dll", "lib/net6.0/Mono.TextTemplating.dll", "lib/netstandard2.0/Mono.TextTemplating.dll", "mono.texttemplating.3.0.0.nupkg.sha512", "mono.texttemplating.nuspec", "readme.md"]}, "Namotion.Reflection/3.1.1": {"sha512": "Qn0wM7u9TpSpja2x8UVexr2bLHb1DGMNhD2TCz3woklxaY1oH+Sitrw9fg/4YbNoNtczeH2jf+yPdXMQlgvFlQ==", "type": "package", "path": "namotion.reflection/3.1.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/Namotion.Reflection.dll", "lib/net462/Namotion.Reflection.xml", "lib/netstandard2.0/Namotion.Reflection.dll", "lib/netstandard2.0/Namotion.Reflection.xml", "namotion.reflection.3.1.1.nupkg.sha512", "namotion.reflection.nuspec"]}, "NETStandard.Library/1.6.1": {"sha512": "WcSp3+vP+yHNgS8EV5J7pZ9IRpeDuARBPN28by8zqff1wJQXm26PVU8L3/fYLBJVU7BtDyqNVWq2KlCVvSSR4A==", "type": "package", "path": "netstandard.library/1.6.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "netstandard.library.1.6.1.nupkg.sha512", "netstandard.library.nuspec"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "NJsonSchema/11.0.0": {"sha512": "yJviNfW8U8+ACJO0VyiNuNIDGoRDZO5awNfjL1+6iO7TVI5pfjun+ZBVsv1hLga/IVlhnUPpMj8VuhQAYfXD/A==", "type": "package", "path": "njsonschema/11.0.0", "files": [".nupkg.metadata", ".signature.p7s", "NuGetIcon.png", "lib/net462/NJsonSchema.dll", "lib/net462/NJsonSchema.xml", "lib/net6.0/NJsonSchema.dll", "lib/net6.0/NJsonSchema.xml", "lib/netstandard2.0/NJsonSchema.dll", "lib/netstandard2.0/NJsonSchema.xml", "njsonschema.11.0.0.nupkg.sha512", "njsonschema.nuspec"]}, "NJsonSchema.Annotations/11.0.0": {"sha512": "kbUrZfspa+Y5Kz0OaRbLQxLtVydWFvkY1CpwfKmravZXG2icphuYHR58EwBZuCQWJb/BL81PGP4FjpDNBFnn6Q==", "type": "package", "path": "njsonschema.annotations/11.0.0", "files": [".nupkg.metadata", ".signature.p7s", "NuGetIcon.png", "lib/net462/NJsonSchema.Annotations.dll", "lib/netstandard2.0/NJsonSchema.Annotations.dll", "njsonschema.annotations.11.0.0.nupkg.sha512", "njsonschema.annotations.nuspec"]}, "NJsonSchema.NewtonsoftJson/11.0.0": {"sha512": "MCugHG7dyQhfwgY1NIaCZNFQzTYwmQpfwm15bksG/Ng1H8up/4DxxH1M9fDtV5xqYBrWGjMRSmTokGr9wwLCPg==", "type": "package", "path": "njsonschema.newtonsoftjson/11.0.0", "files": [".nupkg.metadata", ".signature.p7s", "NuGetIcon.png", "lib/net462/NJsonSchema.NewtonsoftJson.dll", "lib/net462/NJsonSchema.NewtonsoftJson.xml", "lib/netstandard2.0/NJsonSchema.NewtonsoftJson.dll", "lib/netstandard2.0/NJsonSchema.NewtonsoftJson.xml", "njsonschema.newtonsoftjson.11.0.0.nupkg.sha512", "njsonschema.newtonsoftjson.nuspec"]}, "NJsonSchema.Yaml/11.0.0": {"sha512": "riCNrkN2YOVYyvvJjtc7zdIYXQT6/l7FMe0XFGH5rfxWN4/Iy/T60+ZzketAIMhzqn65WPmFZ3NUJ1nGtGCrMw==", "type": "package", "path": "njsonschema.yaml/11.0.0", "files": [".nupkg.metadata", ".signature.p7s", "NuGetIcon.png", "lib/net462/NJsonSchema.Yaml.dll", "lib/netstandard2.0/NJsonSchema.Yaml.dll", "njsonschema.yaml.11.0.0.nupkg.sha512", "njsonschema.yaml.nuspec"]}, "Npgsql/9.0.3": {"sha512": "tPvY61CxOAWxNsKLEBg+oR646X4Bc8UmyQ/tJszL/7mEmIXQnnBhVJZrZEEUv0Bstu0mEsHZD5At3EO8zQRAYw==", "type": "package", "path": "npgsql/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net6.0/Npgsql.dll", "lib/net6.0/Npgsql.xml", "lib/net8.0/Npgsql.dll", "lib/net8.0/Npgsql.xml", "npgsql.9.0.3.nupkg.sha512", "npgsql.nuspec", "postgresql.png"]}, "Npgsql.EntityFrameworkCore.PostgreSQL/9.0.4": {"sha512": "mw5vcY2IEc7L+IeGrxpp/J5OSnCcjkjAgJYCm/eD52wpZze8zsSifdqV7zXslSMmfJG2iIUGZyo3KuDtEFKwMQ==", "type": "package", "path": "npgsql.entityframeworkcore.postgresql/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net8.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll", "lib/net8.0/Npgsql.EntityFrameworkCore.PostgreSQL.xml", "npgsql.entityframeworkcore.postgresql.9.0.4.nupkg.sha512", "npgsql.entityframeworkcore.postgresql.nuspec", "postgresql.png"]}, "NSwag.Annotations/14.0.3": {"sha512": "FOK/EvGfnNcVTRPaZXsngmZJl0uOB51/8M5xSdDYqVLhrqxmSOsL/SIISVecRy/9nbg5dmVsHS20of6papLk5w==", "type": "package", "path": "nswag.annotations/14.0.3", "files": [".nupkg.metadata", ".signature.p7s", "NuGetIcon.png", "lib/net462/NSwag.Annotations.dll", "lib/net462/NSwag.Annotations.xml", "lib/netstandard2.0/NSwag.Annotations.dll", "lib/netstandard2.0/NSwag.Annotations.xml", "nswag.annotations.14.0.3.nupkg.sha512", "nswag.annotations.nuspec"]}, "NSwag.AspNetCore/14.0.3": {"sha512": "GFCf/IIEXwwZ+03py6NpQNz0ARkcLdWxKLYX0EYiiAXoTVBij+g5KIl5/j1FoIH79b30twyNOXcEmdIqA/ZWWQ==", "type": "package", "path": "nswag.aspnetcore/14.0.3", "files": [".nupkg.metadata", ".signature.p7s", "NuGetIcon.png", "build/NSwag.AspNetCore.props", "build/NSwag.AspNetCore.targets", "buildMultiTargeting/NSwag.AspNetCore.props", "buildMultiTargeting/NSwag.AspNetCore.targets", "lib/net462/NSwag.AspNetCore.dll", "lib/net462/NSwag.AspNetCore.xml", "lib/net6.0/NSwag.AspNetCore.dll", "lib/net6.0/NSwag.AspNetCore.xml", "lib/net7.0/NSwag.AspNetCore.dll", "lib/net7.0/NSwag.AspNetCore.xml", "lib/net8.0/NSwag.AspNetCore.dll", "lib/net8.0/NSwag.AspNetCore.xml", "lib/netstandard2.0/NSwag.AspNetCore.dll", "lib/netstandard2.0/NSwag.AspNetCore.xml", "nswag.aspnetcore.14.0.3.nupkg.sha512", "nswag.aspnetcore.nuspec"]}, "NSwag.Core/14.0.3": {"sha512": "lC5NTB2c+/JfQGYOFtVTKy2z8pZD/lsaRGYxzbmJsvNgGfeEq4FiT7yumYEUMvi+lnrv5R58cuqgdLoLdIqqVQ==", "type": "package", "path": "nswag.core/14.0.3", "files": [".nupkg.metadata", ".signature.p7s", "NuGetIcon.png", "lib/net462/NSwag.Core.dll", "lib/net462/NSwag.Core.xml", "lib/netstandard2.0/NSwag.Core.dll", "lib/netstandard2.0/NSwag.Core.xml", "nswag.core.14.0.3.nupkg.sha512", "nswag.core.nuspec"]}, "NSwag.Core.Yaml/14.0.3": {"sha512": "s3PFor6CJ3T2+s5FUh4AaqmPNnsl25rnu01lpqBAy+PIkJjzlMp47xTeBOl0I6bBg5XDcGCiqR/VGjkfRoA/eA==", "type": "package", "path": "nswag.core.yaml/14.0.3", "files": [".nupkg.metadata", ".signature.p7s", "NuGetIcon.png", "lib/net462/NSwag.Core.Yaml.dll", "lib/netstandard2.0/NSwag.Core.Yaml.dll", "nswag.core.yaml.14.0.3.nupkg.sha512", "nswag.core.yaml.nuspec"]}, "NSwag.Generation/14.0.3": {"sha512": "76o7oIymq7oOOMIJLSpMDw9XhP8rgl/RgnFUTY41unRdG8W7Xn1U5VpTmF1I7hI/pgVcp/xwHuTcwgaG1IKnIQ==", "type": "package", "path": "nswag.generation/14.0.3", "files": [".nupkg.metadata", ".signature.p7s", "NuGetIcon.png", "lib/net462/NSwag.Generation.dll", "lib/net462/NSwag.Generation.xml", "lib/netstandard2.0/NSwag.Generation.dll", "lib/netstandard2.0/NSwag.Generation.xml", "nswag.generation.14.0.3.nupkg.sha512", "nswag.generation.nuspec"]}, "NSwag.Generation.AspNetCore/14.0.3": {"sha512": "mMixV2r4/nl8j+7QtkS/5ig5E+5fLhwlTeoMTfdNVY7J9ggyS/xVZlBrgqtgLt9a9gvkNaZMrLu2jacjOvZcqg==", "type": "package", "path": "nswag.generation.aspnetcore/14.0.3", "files": [".nupkg.metadata", ".signature.p7s", "NuGetIcon.png", "lib/net462/NSwag.Generation.AspNetCore.dll", "lib/net462/NSwag.Generation.AspNetCore.xml", "lib/net6.0/NSwag.Generation.AspNetCore.dll", "lib/net6.0/NSwag.Generation.AspNetCore.xml", "lib/net7.0/NSwag.Generation.AspNetCore.dll", "lib/net7.0/NSwag.Generation.AspNetCore.xml", "lib/net8.0/NSwag.Generation.AspNetCore.dll", "lib/net8.0/NSwag.Generation.AspNetCore.xml", "lib/netstandard2.0/NSwag.Generation.AspNetCore.dll", "lib/netstandard2.0/NSwag.Generation.AspNetCore.xml", "nswag.generation.aspnetcore.14.0.3.nupkg.sha512", "nswag.generation.aspnetcore.nuspec"]}, "RestSharp/112.1.0": {"sha512": "bOUGNZqhhv/QeCXHTKEUil846yBjwWXpzPKjO67kFvaAOoF361z7jLY5BMnuZ6WD2WQRA750sNMcK7MSPCQ5Mg==", "type": "package", "path": "restsharp/112.1.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net471/RestSharp.dll", "lib/net471/RestSharp.xml", "lib/net48/RestSharp.dll", "lib/net48/RestSharp.xml", "lib/net6.0/RestSharp.dll", "lib/net6.0/RestSharp.xml", "lib/net7.0/RestSharp.dll", "lib/net7.0/RestSharp.xml", "lib/net8.0/RestSharp.dll", "lib/net8.0/RestSharp.xml", "lib/netstandard2.0/RestSharp.dll", "lib/netstandard2.0/RestSharp.xml", "restsharp.112.1.0.nupkg.sha512", "restsharp.nuspec", "restsharp.png"]}, "Serilog/3.1.1": {"sha512": "P6G4/4Kt9bT635bhuwdXlJ2SCqqn2nhh4gqFqQueCOr9bK/e7W9ll/IoX1Ter948cV2Z/5+5v8pAfJYUISY03A==", "type": "package", "path": "serilog/3.1.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.dll", "lib/net462/Serilog.xml", "lib/net471/Serilog.dll", "lib/net471/Serilog.xml", "lib/net5.0/Serilog.dll", "lib/net5.0/Serilog.xml", "lib/net6.0/Serilog.dll", "lib/net6.0/Serilog.xml", "lib/net7.0/Serilog.dll", "lib/net7.0/Serilog.xml", "lib/netstandard2.0/Serilog.dll", "lib/netstandard2.0/Serilog.xml", "lib/netstandard2.1/Serilog.dll", "lib/netstandard2.1/Serilog.xml", "serilog.3.1.1.nupkg.sha512", "serilog.nuspec"]}, "Serilog.AspNetCore/8.0.1": {"sha512": "B/X+wAfS7yWLVOTD83B+Ip9yl4MkhioaXj90JSoWi1Ayi8XHepEnsBdrkojg08eodCnmOKmShFUN2GgEc6c0CQ==", "type": "package", "path": "serilog.aspnetcore/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.AspNetCore.dll", "lib/net462/Serilog.AspNetCore.xml", "lib/net6.0/Serilog.AspNetCore.dll", "lib/net6.0/Serilog.AspNetCore.xml", "lib/net7.0/Serilog.AspNetCore.dll", "lib/net7.0/Serilog.AspNetCore.xml", "lib/net8.0/Serilog.AspNetCore.dll", "lib/net8.0/Serilog.AspNetCore.xml", "lib/netstandard2.0/Serilog.AspNetCore.dll", "lib/netstandard2.0/Serilog.AspNetCore.xml", "serilog.aspnetcore.8.0.1.nupkg.sha512", "serilog.aspnetcore.nuspec"]}, "Serilog.Extensions.Hosting/8.0.0": {"sha512": "db0OcbWeSCvYQkHWu6n0v40N4kKaTAXNjlM3BKvcbwvNzYphQFcBR+36eQ/7hMMwOkJvAyLC2a9/jNdUL5NjtQ==", "type": "package", "path": "serilog.extensions.hosting/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.Extensions.Hosting.dll", "lib/net462/Serilog.Extensions.Hosting.xml", "lib/net6.0/Serilog.Extensions.Hosting.dll", "lib/net6.0/Serilog.Extensions.Hosting.xml", "lib/net7.0/Serilog.Extensions.Hosting.dll", "lib/net7.0/Serilog.Extensions.Hosting.xml", "lib/net8.0/Serilog.Extensions.Hosting.dll", "lib/net8.0/Serilog.Extensions.Hosting.xml", "lib/netstandard2.0/Serilog.Extensions.Hosting.dll", "lib/netstandard2.0/Serilog.Extensions.Hosting.xml", "serilog.extensions.hosting.8.0.0.nupkg.sha512", "serilog.extensions.hosting.nuspec"]}, "Serilog.Extensions.Logging/8.0.0": {"sha512": "YEAMWu1UnWgf1c1KP85l1SgXGfiVo0Rz6x08pCiPOIBt2Qe18tcZLvdBUuV5o1QHvrs8FAry9wTIhgBRtjIlEg==", "type": "package", "path": "serilog.extensions.logging/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Serilog.Extensions.Logging.dll", "lib/net462/Serilog.Extensions.Logging.xml", "lib/net6.0/Serilog.Extensions.Logging.dll", "lib/net6.0/Serilog.Extensions.Logging.xml", "lib/net7.0/Serilog.Extensions.Logging.dll", "lib/net7.0/Serilog.Extensions.Logging.xml", "lib/net8.0/Serilog.Extensions.Logging.dll", "lib/net8.0/Serilog.Extensions.Logging.xml", "lib/netstandard2.0/Serilog.Extensions.Logging.dll", "lib/netstandard2.0/Serilog.Extensions.Logging.xml", "lib/netstandard2.1/Serilog.Extensions.Logging.dll", "lib/netstandard2.1/Serilog.Extensions.Logging.xml", "serilog-extension-nuget.png", "serilog.extensions.logging.8.0.0.nupkg.sha512", "serilog.extensions.logging.nuspec"]}, "Serilog.Formatting.Compact/2.0.0": {"sha512": "ob6z3ikzFM3D1xalhFuBIK1IOWf+XrQq+H4KeH4VqBcPpNcmUgZlRQ2h3Q7wvthpdZBBoY86qZOI2LCXNaLlNA==", "type": "package", "path": "serilog.formatting.compact/2.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Serilog.Formatting.Compact.dll", "lib/net462/Serilog.Formatting.Compact.xml", "lib/net471/Serilog.Formatting.Compact.dll", "lib/net471/Serilog.Formatting.Compact.xml", "lib/net6.0/Serilog.Formatting.Compact.dll", "lib/net6.0/Serilog.Formatting.Compact.xml", "lib/net7.0/Serilog.Formatting.Compact.dll", "lib/net7.0/Serilog.Formatting.Compact.xml", "lib/netstandard2.0/Serilog.Formatting.Compact.dll", "lib/netstandard2.0/Serilog.Formatting.Compact.xml", "lib/netstandard2.1/Serilog.Formatting.Compact.dll", "lib/netstandard2.1/Serilog.Formatting.Compact.xml", "serilog-extension-nuget.png", "serilog.formatting.compact.2.0.0.nupkg.sha512", "serilog.formatting.compact.nuspec"]}, "Serilog.Settings.Configuration/8.0.0": {"sha512": "nR0iL5HwKj5v6ULo3/zpP8NMcq9E2pxYA6XKTSWCbugVs4YqPyvaqaKOY+OMpPivKp7zMEpax2UKHnDodbRB0Q==", "type": "package", "path": "serilog.settings.configuration/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.Settings.Configuration.dll", "lib/net462/Serilog.Settings.Configuration.xml", "lib/net6.0/Serilog.Settings.Configuration.dll", "lib/net6.0/Serilog.Settings.Configuration.xml", "lib/net7.0/Serilog.Settings.Configuration.dll", "lib/net7.0/Serilog.Settings.Configuration.xml", "lib/net8.0/Serilog.Settings.Configuration.dll", "lib/net8.0/Serilog.Settings.Configuration.xml", "lib/netstandard2.0/Serilog.Settings.Configuration.dll", "lib/netstandard2.0/Serilog.Settings.Configuration.xml", "serilog.settings.configuration.8.0.0.nupkg.sha512", "serilog.settings.configuration.nuspec"]}, "Serilog.Sinks.Console/5.0.0": {"sha512": "IZ6bn79k+3SRXOBpwSOClUHikSkp2toGPCZ0teUkscv4dpDg9E2R2xVsNkLmwddE4OpNVO3N0xiYsAH556vN8Q==", "type": "package", "path": "serilog.sinks.console/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.Sinks.Console.dll", "lib/net462/Serilog.Sinks.Console.xml", "lib/net471/Serilog.Sinks.Console.dll", "lib/net471/Serilog.Sinks.Console.xml", "lib/net5.0/Serilog.Sinks.Console.dll", "lib/net5.0/Serilog.Sinks.Console.xml", "lib/net6.0/Serilog.Sinks.Console.dll", "lib/net6.0/Serilog.Sinks.Console.xml", "lib/net7.0/Serilog.Sinks.Console.dll", "lib/net7.0/Serilog.Sinks.Console.xml", "lib/netstandard2.0/Serilog.Sinks.Console.dll", "lib/netstandard2.0/Serilog.Sinks.Console.xml", "lib/netstandard2.1/Serilog.Sinks.Console.dll", "lib/netstandard2.1/Serilog.Sinks.Console.xml", "serilog.sinks.console.5.0.0.nupkg.sha512", "serilog.sinks.console.nuspec"]}, "Serilog.Sinks.Debug/2.0.0": {"sha512": "Y6g3OBJ4JzTyyw16fDqtFcQ41qQAydnEvEqmXjhwhgjsnG/FaJ8GUqF5ldsC/bVkK8KYmqrPhDO+tm4dF6xx4A==", "type": "package", "path": "serilog.sinks.debug/2.0.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net45/Serilog.Sinks.Debug.dll", "lib/net45/Serilog.Sinks.Debug.xml", "lib/net46/Serilog.Sinks.Debug.dll", "lib/net46/Serilog.Sinks.Debug.xml", "lib/netstandard1.0/Serilog.Sinks.Debug.dll", "lib/netstandard1.0/Serilog.Sinks.Debug.xml", "lib/netstandard2.0/Serilog.Sinks.Debug.dll", "lib/netstandard2.0/Serilog.Sinks.Debug.xml", "lib/netstandard2.1/Serilog.Sinks.Debug.dll", "lib/netstandard2.1/Serilog.Sinks.Debug.xml", "serilog.sinks.debug.2.0.0.nupkg.sha512", "serilog.sinks.debug.nuspec"]}, "Serilog.Sinks.File/5.0.0": {"sha512": "uwV5hdhWPwUH1szhO8PJpFiahqXmzPzJT/sOijH/kFgUx+cyoDTMM8MHD0adw9+Iem6itoibbUXHYslzXsLEAg==", "type": "package", "path": "serilog.sinks.file/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "images/icon.png", "lib/net45/Serilog.Sinks.File.dll", "lib/net45/Serilog.Sinks.File.pdb", "lib/net45/Serilog.Sinks.File.xml", "lib/net5.0/Serilog.Sinks.File.dll", "lib/net5.0/Serilog.Sinks.File.pdb", "lib/net5.0/Serilog.Sinks.File.xml", "lib/netstandard1.3/Serilog.Sinks.File.dll", "lib/netstandard1.3/Serilog.Sinks.File.pdb", "lib/netstandard1.3/Serilog.Sinks.File.xml", "lib/netstandard2.0/Serilog.Sinks.File.dll", "lib/netstandard2.0/Serilog.Sinks.File.pdb", "lib/netstandard2.0/Serilog.Sinks.File.xml", "lib/netstandard2.1/Serilog.Sinks.File.dll", "lib/netstandard2.1/Serilog.Sinks.File.pdb", "lib/netstandard2.1/Serilog.Sinks.File.xml", "serilog.sinks.file.5.0.0.nupkg.sha512", "serilog.sinks.file.nuspec"]}, "Swashbuckle.AspNetCore/8.1.1": {"sha512": "HJHexmU0PiYevgTLvKjYkxEtclF2w4O7iTd3Ef3p6KeT0kcYLpkFVgCw6glpGS57h8769anv8G+NFi9Kge+/yw==", "type": "package", "path": "swashbuckle.aspnetcore/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "build/Swashbuckle.AspNetCore.props", "buildMultiTargeting/Swashbuckle.AspNetCore.props", "docs/package-readme.md", "swashbuckle.aspnetcore.8.1.1.nupkg.sha512", "swashbuckle.aspnetcore.nuspec"]}, "Swashbuckle.AspNetCore.Annotations/8.1.1": {"sha512": "7HPeJGAs2VikmUOUHjmXo657FhUEuwajjgUmLTVrzGHo4tS1Io29cyMMfMDp4eAnXnY88jMa4MwG00xhWRgIDg==", "type": "package", "path": "swashbuckle.aspnetcore.annotations/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net8.0/Swashbuckle.AspNetCore.Annotations.dll", "lib/net8.0/Swashbuckle.AspNetCore.Annotations.pdb", "lib/net8.0/Swashbuckle.AspNetCore.Annotations.xml", "lib/net9.0/Swashbuckle.AspNetCore.Annotations.dll", "lib/net9.0/Swashbuckle.AspNetCore.Annotations.pdb", "lib/net9.0/Swashbuckle.AspNetCore.Annotations.xml", "lib/netstandard2.0/Swashbuckle.AspNetCore.Annotations.dll", "lib/netstandard2.0/Swashbuckle.AspNetCore.Annotations.pdb", "lib/netstandard2.0/Swashbuckle.AspNetCore.Annotations.xml", "package-readme.md", "swashbuckle.aspnetcore.annotations.8.1.1.nupkg.sha512", "swashbuckle.aspnetcore.annotations.nuspec"]}, "Swashbuckle.AspNetCore.Swagger/8.1.1": {"sha512": "h+8D5jQtnl6X4f2hJQwf0Khj0SnCQANzirCELjXJ6quJ4C1aNNCvJrAsQ+4fOKAMqJkvW48cKj79ftG+YoGcRg==", "type": "package", "path": "swashbuckle.aspnetcore.swagger/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net8.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/net8.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/net8.0/Swashbuckle.AspNetCore.Swagger.xml", "lib/net9.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/net9.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/net9.0/Swashbuckle.AspNetCore.Swagger.xml", "lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.xml", "package-readme.md", "swashbuckle.aspnetcore.swagger.8.1.1.nupkg.sha512", "swashbuckle.aspnetcore.swagger.nuspec"]}, "Swashbuckle.AspNetCore.SwaggerGen/8.1.1": {"sha512": "2EuPzXSNleOOzYvziERWRLnk1Oz9i0Z1PimaUFy1SasBqeV/rG+eMfwFAMtTaf4W6gvVOzRcUCNRHvpBIIzr+A==", "type": "package", "path": "swashbuckle.aspnetcore.swaggergen/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net8.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/net8.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/net8.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "lib/net9.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/net9.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/net9.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "package-readme.md", "swashbuckle.aspnetcore.swaggergen.8.1.1.nupkg.sha512", "swashbuckle.aspnetcore.swaggergen.nuspec"]}, "Swashbuckle.AspNetCore.SwaggerUI/8.1.1": {"sha512": "GDLX/MpK4oa2nYC1N/zN2UidQTtVKLPF6gkdEmGb0RITEwpJG9Gu8olKqPYnKqVeFn44JZoCS0M2LGRKXP8B/A==", "type": "package", "path": "swashbuckle.aspnetcore.swaggerui/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net8.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/net8.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/net8.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "lib/net9.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/net9.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/net9.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "package-readme.md", "swashbuckle.aspnetcore.swaggerui.8.1.1.nupkg.sha512", "swashbuckle.aspnetcore.swaggerui.nuspec"]}, "System.ClientModel/1.0.0": {"sha512": "I3CVkvxeqFYjIVEP59DnjbeoGNfo/+SZrCLpRz2v/g0gpCHaEMPtWSY0s9k/7jR1rAsLNg2z2u1JRB76tPjnIw==", "type": "package", "path": "system.clientmodel/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "DotNetPackageIcon.png", "README.md", "lib/net6.0/System.ClientModel.dll", "lib/net6.0/System.ClientModel.xml", "lib/netstandard2.0/System.ClientModel.dll", "lib/netstandard2.0/System.ClientModel.xml", "system.clientmodel.1.0.0.nupkg.sha512", "system.clientmodel.nuspec"]}, "System.CodeDom/6.0.0": {"sha512": "CPc6tWO1LAer3IzfZufDBRL+UZQcj5uS207NHALQzP84Vp/z6wF0Aa0YZImOQY8iStY0A2zI/e3ihKNPfUm8XA==", "type": "package", "path": "system.codedom/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.CodeDom.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.CodeDom.dll", "lib/net461/System.CodeDom.xml", "lib/net6.0/System.CodeDom.dll", "lib/net6.0/System.CodeDom.xml", "lib/netstandard2.0/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.xml", "system.codedom.6.0.0.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition/7.0.0": {"sha512": "tRwgcAkDd85O8Aq6zHDANzQaq380cek9lbMg5Qma46u5BZXq/G+XvIYmu+UI+BIIZ9zssXLYrkTykEqxxvhcmg==", "type": "package", "path": "system.composition/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Composition.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Composition.targets", "lib/net461/_._", "lib/netcoreapp2.0/_._", "lib/netstandard2.0/_._", "system.composition.7.0.0.nupkg.sha512", "system.composition.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.AttributedModel/7.0.0": {"sha512": "2QzClqjElKxgI1jK1Jztnq44/8DmSuTSGGahXqQ4TdEV0h9s2KikQZIgcEqVzR7OuWDFPGLHIprBJGQEPr8fAQ==", "type": "package", "path": "system.composition.attributedmodel/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Composition.AttributedModel.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Composition.AttributedModel.targets", "lib/net462/System.Composition.AttributedModel.dll", "lib/net462/System.Composition.AttributedModel.xml", "lib/net6.0/System.Composition.AttributedModel.dll", "lib/net6.0/System.Composition.AttributedModel.xml", "lib/net7.0/System.Composition.AttributedModel.dll", "lib/net7.0/System.Composition.AttributedModel.xml", "lib/netstandard2.0/System.Composition.AttributedModel.dll", "lib/netstandard2.0/System.Composition.AttributedModel.xml", "system.composition.attributedmodel.7.0.0.nupkg.sha512", "system.composition.attributedmodel.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.Convention/7.0.0": {"sha512": "IMhTlpCs4HmlD8B+J8/kWfwX7vrBBOs6xyjSTzBlYSs7W4OET4tlkR/Sg9NG8jkdJH9Mymq0qGdYS1VPqRTBnQ==", "type": "package", "path": "system.composition.convention/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Composition.Convention.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Composition.Convention.targets", "lib/net462/System.Composition.Convention.dll", "lib/net462/System.Composition.Convention.xml", "lib/net6.0/System.Composition.Convention.dll", "lib/net6.0/System.Composition.Convention.xml", "lib/net7.0/System.Composition.Convention.dll", "lib/net7.0/System.Composition.Convention.xml", "lib/netstandard2.0/System.Composition.Convention.dll", "lib/netstandard2.0/System.Composition.Convention.xml", "system.composition.convention.7.0.0.nupkg.sha512", "system.composition.convention.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.Hosting/7.0.0": {"sha512": "eB6gwN9S+54jCTBJ5bpwMOVerKeUfGGTYCzz3QgDr1P55Gg/Wb27ShfPIhLMjmZ3MoAKu8uUSv6fcCdYJTN7Bg==", "type": "package", "path": "system.composition.hosting/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Composition.Hosting.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Composition.Hosting.targets", "lib/net462/System.Composition.Hosting.dll", "lib/net462/System.Composition.Hosting.xml", "lib/net6.0/System.Composition.Hosting.dll", "lib/net6.0/System.Composition.Hosting.xml", "lib/net7.0/System.Composition.Hosting.dll", "lib/net7.0/System.Composition.Hosting.xml", "lib/netstandard2.0/System.Composition.Hosting.dll", "lib/netstandard2.0/System.Composition.Hosting.xml", "system.composition.hosting.7.0.0.nupkg.sha512", "system.composition.hosting.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.Runtime/7.0.0": {"sha512": "aZJ1Zr5Txe925rbo4742XifEyW0MIni1eiUebmcrP3HwLXZ3IbXUj4MFMUH/RmnJOAQiS401leg/2Sz1MkApDw==", "type": "package", "path": "system.composition.runtime/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Composition.Runtime.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Composition.Runtime.targets", "lib/net462/System.Composition.Runtime.dll", "lib/net462/System.Composition.Runtime.xml", "lib/net6.0/System.Composition.Runtime.dll", "lib/net6.0/System.Composition.Runtime.xml", "lib/net7.0/System.Composition.Runtime.dll", "lib/net7.0/System.Composition.Runtime.xml", "lib/netstandard2.0/System.Composition.Runtime.dll", "lib/netstandard2.0/System.Composition.Runtime.xml", "system.composition.runtime.7.0.0.nupkg.sha512", "system.composition.runtime.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.TypedParts/7.0.0": {"sha512": "ZK0KNPfbtxVceTwh+oHNGUOYV2WNOHReX2AXipuvkURC7s/jPwoWfsu3SnDBDgofqbiWr96geofdQ2erm/KTHg==", "type": "package", "path": "system.composition.typedparts/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Composition.TypedParts.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Composition.TypedParts.targets", "lib/net462/System.Composition.TypedParts.dll", "lib/net462/System.Composition.TypedParts.xml", "lib/net6.0/System.Composition.TypedParts.dll", "lib/net6.0/System.Composition.TypedParts.xml", "lib/net7.0/System.Composition.TypedParts.dll", "lib/net7.0/System.Composition.TypedParts.xml", "lib/netstandard2.0/System.Composition.TypedParts.dll", "lib/netstandard2.0/System.Composition.TypedParts.xml", "system.composition.typedparts.7.0.0.nupkg.sha512", "system.composition.typedparts.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.DiagnosticSource/9.0.5": {"sha512": "WoI5or8kY2VxFdDmsaRZ5yaYvvb+4MCyy66eXo79Cy1uMa7qXeGIlYmZx7R9Zy5S4xZjmqvkk2V8L6/vDwAAEA==", "type": "package", "path": "system.diagnostics.diagnosticsource/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.DiagnosticSource.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets", "lib/net462/System.Diagnostics.DiagnosticSource.dll", "lib/net462/System.Diagnostics.DiagnosticSource.xml", "lib/net8.0/System.Diagnostics.DiagnosticSource.dll", "lib/net8.0/System.Diagnostics.DiagnosticSource.xml", "lib/net9.0/System.Diagnostics.DiagnosticSource.dll", "lib/net9.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.9.0.5.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt"]}, "System.Drawing.Common/8.0.4": {"sha512": "3G4xpa8mUYGzEF0HlswlBArAFywHJIzsZoB5hU4yMlnYHaabj/lg019BwbyyYBxj0aoM7Cz+jdlgUemeno9LOQ==", "type": "package", "path": "system.drawing.common/8.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Drawing.Common.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Drawing.Common.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Drawing.Common.dll", "lib/net462/System.Drawing.Common.pdb", "lib/net462/System.Drawing.Common.xml", "lib/net6.0/System.Drawing.Common.dll", "lib/net6.0/System.Drawing.Common.pdb", "lib/net6.0/System.Drawing.Common.xml", "lib/net7.0/System.Drawing.Common.dll", "lib/net7.0/System.Drawing.Common.pdb", "lib/net7.0/System.Drawing.Common.xml", "lib/net8.0/System.Drawing.Common.dll", "lib/net8.0/System.Drawing.Common.pdb", "lib/net8.0/System.Drawing.Common.xml", "lib/netstandard2.0/System.Drawing.Common.dll", "lib/netstandard2.0/System.Drawing.Common.pdb", "lib/netstandard2.0/System.Drawing.Common.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "system.drawing.common.8.0.4.nupkg.sha512", "system.drawing.common.nuspec", "useSharedDesignerContext.txt"]}, "System.Formats.Asn1/9.0.1": {"sha512": "efRn3TXUx2aWG4yOjD5jxTYlPy4Pz/8fiwYBtIpVb/+ySsNA9PFWFd3M3MdcRx1XjpYtj5QSXGm8XnGCeh7dSA==", "type": "package", "path": "system.formats.asn1/9.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Formats.Asn1.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Formats.Asn1.targets", "lib/net462/System.Formats.Asn1.dll", "lib/net462/System.Formats.Asn1.xml", "lib/net8.0/System.Formats.Asn1.dll", "lib/net8.0/System.Formats.Asn1.xml", "lib/net9.0/System.Formats.Asn1.dll", "lib/net9.0/System.Formats.Asn1.xml", "lib/netstandard2.0/System.Formats.Asn1.dll", "lib/netstandard2.0/System.Formats.Asn1.xml", "system.formats.asn1.9.0.1.nupkg.sha512", "system.formats.asn1.nuspec", "useSharedDesignerContext.txt"]}, "System.IdentityModel.Tokens.Jwt/8.1.2": {"sha512": "UoidlNYjML1ZbV5s8bLP84VpxDzv8uhHzyt5YkZwqLmFTmtOQheNuTKpR/5UWmO5Ka4JT3kVmhUNq5Li733wTg==", "type": "package", "path": "system.identitymodel.tokens.jwt/8.1.2", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/System.IdentityModel.Tokens.Jwt.dll", "lib/net462/System.IdentityModel.Tokens.Jwt.xml", "lib/net472/System.IdentityModel.Tokens.Jwt.dll", "lib/net472/System.IdentityModel.Tokens.Jwt.xml", "lib/net6.0/System.IdentityModel.Tokens.Jwt.dll", "lib/net6.0/System.IdentityModel.Tokens.Jwt.xml", "lib/net8.0/System.IdentityModel.Tokens.Jwt.dll", "lib/net8.0/System.IdentityModel.Tokens.Jwt.xml", "lib/net9.0/System.IdentityModel.Tokens.Jwt.dll", "lib/net9.0/System.IdentityModel.Tokens.Jwt.xml", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.xml", "system.identitymodel.tokens.jwt.8.1.2.nupkg.sha512", "system.identitymodel.tokens.jwt.nuspec"]}, "System.IO.Packaging/4.7.0": {"sha512": "9VV4KAbgRQZ79iEoG40KIeZy38O30oWwewScAST879+oki8g/Wa2HXZQgrhDDxQM4GkP1PnRJll05NMiVPbYAw==", "type": "package", "path": "system.io.packaging/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.IO.Packaging.dll", "lib/net46/System.IO.Packaging.xml", "lib/netstandard1.3/System.IO.Packaging.dll", "lib/netstandard1.3/System.IO.Packaging.xml", "lib/netstandard2.0/System.IO.Packaging.dll", "lib/netstandard2.0/System.IO.Packaging.xml", "ref/net46/System.IO.Packaging.dll", "ref/net46/System.IO.Packaging.xml", "ref/netstandard1.3/System.IO.Packaging.dll", "ref/netstandard1.3/System.IO.Packaging.xml", "ref/netstandard2.0/System.IO.Packaging.dll", "ref/netstandard2.0/System.IO.Packaging.xml", "system.io.packaging.4.7.0.nupkg.sha512", "system.io.packaging.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.IO.Pipelines/9.0.5": {"sha512": "5WXo+3MGcnYn54+1ojf+kRzKq1Q6sDUnovujNJ2ky1nl1/kP3+PMil9LPbFvZ2mkhvAGmQcY07G2sfHat/v0Fw==", "type": "package", "path": "system.io.pipelines/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Pipelines.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Pipelines.targets", "lib/net462/System.IO.Pipelines.dll", "lib/net462/System.IO.Pipelines.xml", "lib/net8.0/System.IO.Pipelines.dll", "lib/net8.0/System.IO.Pipelines.xml", "lib/net9.0/System.IO.Pipelines.dll", "lib/net9.0/System.IO.Pipelines.xml", "lib/netstandard2.0/System.IO.Pipelines.dll", "lib/netstandard2.0/System.IO.Pipelines.xml", "system.io.pipelines.9.0.5.nupkg.sha512", "system.io.pipelines.nuspec", "useSharedDesignerContext.txt"]}, "System.Memory.Data/1.0.2": {"sha512": "JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "type": "package", "path": "system.memory.data/1.0.2", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "DotNetPackageIcon.png", "README.md", "lib/net461/System.Memory.Data.dll", "lib/net461/System.Memory.Data.xml", "lib/netstandard2.0/System.Memory.Data.dll", "lib/netstandard2.0/System.Memory.Data.xml", "system.memory.data.1.0.2.nupkg.sha512", "system.memory.data.nuspec"]}, "System.Security.Cryptography.Pkcs/9.0.1": {"sha512": "782UEbbkZE5tiYYsUQWn4VuzJCvpJRp3umlC9oEELyWZAB1swMZr/KMlJN/0+BSVolYt9OnbOKqWbcoHbdpIeg==", "type": "package", "path": "system.security.cryptography.pkcs/9.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.Pkcs.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Pkcs.targets", "lib/net462/System.Security.Cryptography.Pkcs.dll", "lib/net462/System.Security.Cryptography.Pkcs.xml", "lib/net8.0/System.Security.Cryptography.Pkcs.dll", "lib/net8.0/System.Security.Cryptography.Pkcs.xml", "lib/net9.0/System.Security.Cryptography.Pkcs.dll", "lib/net9.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net9.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net9.0/System.Security.Cryptography.Pkcs.xml", "system.security.cryptography.pkcs.9.0.1.nupkg.sha512", "system.security.cryptography.pkcs.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.ProtectedData/4.7.0": {"sha512": "ehYW0m9ptxpGWvE4zgqongBVWpSDU/JCFD4K7krxkQwSz/sFQjEXCUqpvencjy6DYDbn7Ig09R8GFffu8TtneQ==", "type": "package", "path": "system.security.cryptography.protecteddata/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.ProtectedData.dll", "lib/net461/System.Security.Cryptography.ProtectedData.dll", "lib/net461/System.Security.Cryptography.ProtectedData.xml", "lib/netstandard1.3/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.ProtectedData.dll", "ref/net461/System.Security.Cryptography.ProtectedData.dll", "ref/net461/System.Security.Cryptography.ProtectedData.xml", "ref/netstandard1.3/System.Security.Cryptography.ProtectedData.dll", "ref/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "ref/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/win/lib/net46/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/net461/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/net461/System.Security.Cryptography.ProtectedData.xml", "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "system.security.cryptography.protecteddata.4.7.0.nupkg.sha512", "system.security.cryptography.protecteddata.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Cryptography.Xml/8.0.1": {"sha512": "hqu2ztecOf3BYg5q1R7QcyliX9L7r3mfsWtaRitAxcezH8hyZMB7zCmhi186hsUZXk1KxsAHXwyPEW+xvUED6g==", "type": "package", "path": "system.security.cryptography.xml/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.Xml.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Xml.targets", "lib/net462/System.Security.Cryptography.Xml.dll", "lib/net462/System.Security.Cryptography.Xml.xml", "lib/net6.0/System.Security.Cryptography.Xml.dll", "lib/net6.0/System.Security.Cryptography.Xml.xml", "lib/net7.0/System.Security.Cryptography.Xml.dll", "lib/net7.0/System.Security.Cryptography.Xml.xml", "lib/net8.0/System.Security.Cryptography.Xml.dll", "lib/net8.0/System.Security.Cryptography.Xml.xml", "lib/netstandard2.0/System.Security.Cryptography.Xml.dll", "lib/netstandard2.0/System.Security.Cryptography.Xml.xml", "system.security.cryptography.xml.8.0.1.nupkg.sha512", "system.security.cryptography.xml.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Encoding.CodePages/9.0.1": {"sha512": "7Op2OR5RKJgviMpPsXbcCN5bxew/KmW1AM3bzwFo3kShenYjy9eV5lYYuIxWinhrLMooGu6t2ktMRN9toV7l0Q==", "type": "package", "path": "system.text.encoding.codepages/9.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Text.Encoding.CodePages.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Text.Encoding.CodePages.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Text.Encoding.CodePages.dll", "lib/net462/System.Text.Encoding.CodePages.xml", "lib/net8.0/System.Text.Encoding.CodePages.dll", "lib/net8.0/System.Text.Encoding.CodePages.xml", "lib/net9.0/System.Text.Encoding.CodePages.dll", "lib/net9.0/System.Text.Encoding.CodePages.xml", "lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "lib/netstandard2.0/System.Text.Encoding.CodePages.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net8.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net8.0/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/net9.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net9.0/System.Text.Encoding.CodePages.xml", "system.text.encoding.codepages.9.0.1.nupkg.sha512", "system.text.encoding.codepages.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Encodings.Web/9.0.5": {"sha512": "HJPmqP2FsE+WVUUlTsZ4IFRSyzw40yz0ubiTnsaqm+Xo5fFZhVRvx6Zn8tLXj92/6pbre6OA4QL2A2vnCSKxJA==", "type": "package", "path": "system.text.encodings.web/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Text.Encodings.Web.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Text.Encodings.Web.targets", "lib/net462/System.Text.Encodings.Web.dll", "lib/net462/System.Text.Encodings.Web.xml", "lib/net8.0/System.Text.Encodings.Web.dll", "lib/net8.0/System.Text.Encodings.Web.xml", "lib/net9.0/System.Text.Encodings.Web.dll", "lib/net9.0/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net9.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net9.0/System.Text.Encodings.Web.xml", "system.text.encodings.web.9.0.5.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/9.0.5": {"sha512": "rnP61ZfloTgPQPe7ecr36loNiGX3g1PocxlKHdY/FUpDSsExKkTxpMAlB4X35wNEPr1X7mkYZuQvW3Lhxmu7KA==", "type": "package", "path": "system.text.json/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net8.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/net9.0/System.Text.Json.dll", "lib/net9.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.9.0.5.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "YamlDotNet/13.7.1": {"sha512": "X4m1PnFcJwvAj1sCDMntg/eZcX96CJLrWMiYnq41KqhFVZPuw63ZTSxIGqgdCwHWHvCAyTxheELC/VDf1HsU2A==", "type": "package", "path": "yamldotnet/13.7.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "images/yamldotnet.png", "lib/net35/YamlDotNet.dll", "lib/net35/YamlDotNet.xml", "lib/net45/YamlDotNet.dll", "lib/net45/YamlDotNet.xml", "lib/net47/YamlDotNet.dll", "lib/net47/YamlDotNet.xml", "lib/net6.0/YamlDotNet.dll", "lib/net6.0/YamlDotNet.xml", "lib/net7.0/YamlDotNet.dll", "lib/net7.0/YamlDotNet.xml", "lib/netstandard2.0/YamlDotNet.dll", "lib/netstandard2.0/YamlDotNet.xml", "lib/netstandard2.1/YamlDotNet.dll", "lib/netstandard2.1/YamlDotNet.xml", "yamldotnet.13.7.1.nupkg.sha512", "yamldotnet.nuspec"]}, "Abstraction/1.0.0": {"type": "project", "path": "../Abstraction/Abstraction.csproj", "msbuildProject": "../Abstraction/Abstraction.csproj"}, "Application/1.0.0": {"type": "project", "path": "../Application/Application.csproj", "msbuildProject": "../Application/Application.csproj"}, "Domain/1.0.0": {"type": "project", "path": "../Domain/Domain.csproj", "msbuildProject": "../Domain/Domain.csproj"}, "Infrastructure/1.0.0": {"type": "project", "path": "../Infrastructure/Infrastructure.csproj", "msbuildProject": "../Infrastructure/Infrastructure.csproj"}, "Migrators/1.0.0": {"type": "project", "path": "../Migrators/Migrators.csproj", "msbuildProject": "../Migrators/Migrators.csproj"}, "Shared/1.0.0": {"type": "project", "path": "../Shared/Shared.csproj", "msbuildProject": "../Shared/Shared.csproj"}}, "projectFileDependencyGroups": {"net8.0": ["Finbuckle.MultiTenant.EntityFrameworkCore >= 6.13.1", "Infrastructure >= 1.0.0", "Microsoft.AspNetCore.Mvc.Versioning >= 5.1.0", "Microsoft.AspNetCore.OpenApi >= 8.0.11", "Microsoft.EntityFrameworkCore.Design >= 9.0.5", "Microsoft.VisualStudio.Azure.Containers.Tools.Targets >= 1.21.0", "Migrators >= 1.0.0", "Swashbuckle.AspNetCore >= 8.1.1", "Swashbuckle.AspNetCore.Annotations >= 8.1.1"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Web.Host.csproj", "projectName": "Web.Host", "projectPath": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\Web.Host.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Web.Host\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Infrastructure.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Infrastructure\\Infrastructure.csproj"}, "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Migrators\\Migrators.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\leadrat\\this-applications\\This.API\\Migrators\\Migrators.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Finbuckle.MultiTenant.EntityFrameworkCore": {"target": "Package", "version": "[6.13.1, )"}, "Microsoft.AspNetCore.Mvc.Versioning": {"target": "Package", "version": "[5.1.0, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.5, )"}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": {"target": "Package", "version": "[1.21.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[8.1.1, )"}, "Swashbuckle.AspNetCore.Annotations": {"target": "Package", "version": "[8.1.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.14, 8.0.14]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[8.0.14, 8.0.14]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.14, 8.0.14]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[8.0.14, 8.0.14]"}], "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.3.25201.16/PortableRuntimeIdentifierGraph.json", "packagesToPrune": {"Microsoft.AspNetCore": "(, 8.0.0]", "Microsoft.AspNetCore.Antiforgery": "(, 8.0.0]", "Microsoft.AspNetCore.Authentication": "(, 8.0.0]", "Microsoft.AspNetCore.Authentication.Abstractions": "(, 8.0.0]", "Microsoft.AspNetCore.Authentication.BearerToken": "(, 8.0.0]", "Microsoft.AspNetCore.Authentication.Cookies": "(, 8.0.0]", "Microsoft.AspNetCore.Authentication.Core": "(, 8.0.0]", "Microsoft.AspNetCore.Authentication.OAuth": "(, 8.0.0]", "Microsoft.AspNetCore.Authorization": "(, 8.0.0]", "Microsoft.AspNetCore.Authorization.Policy": "(, 8.0.0]", "Microsoft.AspNetCore.Components": "(, 8.0.0]", "Microsoft.AspNetCore.Components.Authorization": "(, 8.0.0]", "Microsoft.AspNetCore.Components.Endpoints": "(, 8.0.0]", "Microsoft.AspNetCore.Components.Forms": "(, 8.0.0]", "Microsoft.AspNetCore.Components.Server": "(, 8.0.0]", "Microsoft.AspNetCore.Components.Web": "(, 8.0.0]", "Microsoft.AspNetCore.Connections.Abstractions": "(, 8.0.0]", "Microsoft.AspNetCore.CookiePolicy": "(, 8.0.0]", "Microsoft.AspNetCore.Cors": "(, 8.0.0]", "Microsoft.AspNetCore.Cryptography.Internal": "(, 8.0.0]", "Microsoft.AspNetCore.Cryptography.KeyDerivation": "(, 8.0.0]", "Microsoft.AspNetCore.DataProtection": "(, 8.0.0]", "Microsoft.AspNetCore.DataProtection.Abstractions": "(, 8.0.0]", "Microsoft.AspNetCore.DataProtection.Extensions": "(, 8.0.0]", "Microsoft.AspNetCore.Diagnostics": "(, 8.0.0]", "Microsoft.AspNetCore.Diagnostics.Abstractions": "(, 8.0.0]", "Microsoft.AspNetCore.Diagnostics.HealthChecks": "(, 8.0.0]", "Microsoft.AspNetCore.HostFiltering": "(, 8.0.0]", "Microsoft.AspNetCore.Hosting": "(, 8.0.0]", "Microsoft.AspNetCore.Hosting.Abstractions": "(, 8.0.0]", "Microsoft.AspNetCore.Hosting.Server.Abstractions": "(, 8.0.0]", "Microsoft.AspNetCore.Html.Abstractions": "(, 8.0.0]", "Microsoft.AspNetCore.Http": "(, 8.0.0]", "Microsoft.AspNetCore.Http.Abstractions": "(, 8.0.0]", "Microsoft.AspNetCore.Http.Connections": "(, 8.0.0]", "Microsoft.AspNetCore.Http.Connections.Common": "(, 8.0.0]", "Microsoft.AspNetCore.Http.Extensions": "(, 8.0.0]", "Microsoft.AspNetCore.Http.Features": "(, 8.0.0]", "Microsoft.AspNetCore.Http.Results": "(, 8.0.0]", "Microsoft.AspNetCore.HttpLogging": "(, 8.0.0]", "Microsoft.AspNetCore.HttpOverrides": "(, 8.0.0]", "Microsoft.AspNetCore.HttpsPolicy": "(, 8.0.0]", "Microsoft.AspNetCore.Identity": "(, 8.0.0]", "Microsoft.AspNetCore.Localization": "(, 8.0.0]", "Microsoft.AspNetCore.Localization.Routing": "(, 8.0.0]", "Microsoft.AspNetCore.Metadata": "(, 8.0.0]", "Microsoft.AspNetCore.Mvc": "(, 8.0.0]", "Microsoft.AspNetCore.Mvc.Abstractions": "(, 8.0.0]", "Microsoft.AspNetCore.Mvc.ApiExplorer": "(, 8.0.0]", "Microsoft.AspNetCore.Mvc.Core": "(, 8.0.0]", "Microsoft.AspNetCore.Mvc.Cors": "(, 8.0.0]", "Microsoft.AspNetCore.Mvc.DataAnnotations": "(, 8.0.0]", "Microsoft.AspNetCore.Mvc.Formatters.Json": "(, 8.0.0]", "Microsoft.AspNetCore.Mvc.Formatters.Xml": "(, 8.0.0]", "Microsoft.AspNetCore.Mvc.Localization": "(, 8.0.0]", "Microsoft.AspNetCore.Mvc.Razor": "(, 8.0.0]", "Microsoft.AspNetCore.Mvc.RazorPages": "(, 8.0.0]", "Microsoft.AspNetCore.Mvc.TagHelpers": "(, 8.0.0]", "Microsoft.AspNetCore.Mvc.ViewFeatures": "(, 8.0.0]", "Microsoft.AspNetCore.OutputCaching": "(, 8.0.0]", "Microsoft.AspNetCore.RateLimiting": "(, 8.0.0]", "Microsoft.AspNetCore.Razor": "(, 8.0.0]", "Microsoft.AspNetCore.Razor.Runtime": "(, 8.0.0]", "Microsoft.AspNetCore.RequestDecompression": "(, 8.0.0]", "Microsoft.AspNetCore.ResponseCaching": "(, 8.0.0]", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "(, 8.0.0]", "Microsoft.AspNetCore.ResponseCompression": "(, 8.0.0]", "Microsoft.AspNetCore.Rewrite": "(, 8.0.0]", "Microsoft.AspNetCore.Routing": "(, 8.0.0]", "Microsoft.AspNetCore.Routing.Abstractions": "(, 8.0.0]", "Microsoft.AspNetCore.Server.HttpSys": "(, 8.0.0]", "Microsoft.AspNetCore.Server.IIS": "(, 8.0.0]", "Microsoft.AspNetCore.Server.IISIntegration": "(, 8.0.0]", "Microsoft.AspNetCore.Server.Kestrel": "(, 8.0.0]", "Microsoft.AspNetCore.Server.Kestrel.Core": "(, 8.0.0]", "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes": "(, 8.0.0]", "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic": "(, 8.0.0]", "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets": "(, 8.0.0]", "Microsoft.AspNetCore.Session": "(, 8.0.0]", "Microsoft.AspNetCore.SignalR": "(, 8.0.0]", "Microsoft.AspNetCore.SignalR.Common": "(, 8.0.0]", "Microsoft.AspNetCore.SignalR.Core": "(, 8.0.0]", "Microsoft.AspNetCore.SignalR.Protocols.Json": "(, 8.0.0]", "Microsoft.AspNetCore.StaticFiles": "(, 8.0.0]", "Microsoft.AspNetCore.WebSockets": "(, 8.0.0]", "Microsoft.AspNetCore.WebUtilities": "(, 8.0.0]", "Microsoft.CSharp": "(, 4.7.0]", "Microsoft.Extensions.Caching.Abstractions": "(, 8.0.0]", "Microsoft.Extensions.Caching.Memory": "(, 8.0.0]", "Microsoft.Extensions.Configuration": "(, 8.0.0]", "Microsoft.Extensions.Configuration.Abstractions": "(, 8.0.0]", "Microsoft.Extensions.Configuration.Binder": "(, 8.0.0]", "Microsoft.Extensions.Configuration.CommandLine": "(, 8.0.0]", "Microsoft.Extensions.Configuration.EnvironmentVariables": "(, 8.0.0]", "Microsoft.Extensions.Configuration.FileExtensions": "(, 8.0.0]", "Microsoft.Extensions.Configuration.Ini": "(, 8.0.0]", "Microsoft.Extensions.Configuration.Json": "(, 8.0.0]", "Microsoft.Extensions.Configuration.KeyPerFile": "(, 8.0.0]", "Microsoft.Extensions.Configuration.UserSecrets": "(, 8.0.0]", "Microsoft.Extensions.Configuration.Xml": "(, 8.0.0]", "Microsoft.Extensions.DependencyInjection": "(, 8.0.0]", "Microsoft.Extensions.DependencyInjection.Abstractions": "(, 8.0.0]", "Microsoft.Extensions.Diagnostics": "(, 8.0.0]", "Microsoft.Extensions.Diagnostics.Abstractions": "(, 8.0.0]", "Microsoft.Extensions.Diagnostics.HealthChecks": "(, 8.0.0]", "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "(, 8.0.0]", "Microsoft.Extensions.Features": "(, 8.0.0]", "Microsoft.Extensions.FileProviders.Abstractions": "(, 8.0.0]", "Microsoft.Extensions.FileProviders.Composite": "(, 8.0.0]", "Microsoft.Extensions.FileProviders.Embedded": "(, 8.0.0]", "Microsoft.Extensions.FileProviders.Physical": "(, 8.0.0]", "Microsoft.Extensions.FileSystemGlobbing": "(, 8.0.0]", "Microsoft.Extensions.Hosting": "(, 8.0.0]", "Microsoft.Extensions.Hosting.Abstractions": "(, 8.0.0]", "Microsoft.Extensions.Http": "(, 8.0.0]", "Microsoft.Extensions.Identity.Core": "(, 8.0.0]", "Microsoft.Extensions.Identity.Stores": "(, 8.0.0]", "Microsoft.Extensions.Localization": "(, 8.0.0]", "Microsoft.Extensions.Localization.Abstractions": "(, 8.0.0]", "Microsoft.Extensions.Logging": "(, 8.0.0]", "Microsoft.Extensions.Logging.Abstractions": "(, 8.0.0]", "Microsoft.Extensions.Logging.Configuration": "(, 8.0.0]", "Microsoft.Extensions.Logging.Console": "(, 8.0.0]", "Microsoft.Extensions.Logging.Debug": "(, 8.0.0]", "Microsoft.Extensions.Logging.EventLog": "(, 8.0.0]", "Microsoft.Extensions.Logging.EventSource": "(, 8.0.0]", "Microsoft.Extensions.Logging.TraceSource": "(, 8.0.0]", "Microsoft.Extensions.ObjectPool": "(, 8.0.0]", "Microsoft.Extensions.Options": "(, 8.0.0]", "Microsoft.Extensions.Options.ConfigurationExtensions": "(, 8.0.0]", "Microsoft.Extensions.Options.DataAnnotations": "(, 8.0.0]", "Microsoft.Extensions.Primitives": "(, 8.0.0]", "Microsoft.Extensions.WebEncoders": "(, 8.0.0]", "Microsoft.JSInterop": "(, 8.0.0]", "Microsoft.Net.Http.Headers": "(, 8.0.0]", "Microsoft.NETCore.App": "(, 2.1.0]", "Microsoft.VisualBasic": "(, 10.3.0]", "Microsoft.Win32.Primitives": "(, 4.3.0]", "Microsoft.Win32.Registry": "(, 5.0.0]", "Microsoft.Win32.SystemEvents": "(, 5.0.0]", "runtime.any.System.Collections": "(, 4.3.0]", "runtime.any.System.Diagnostics.Tools": "(, 4.3.0]", "runtime.any.System.Diagnostics.Tracing": "(, 4.3.0]", "runtime.any.System.Globalization": "(, 4.3.0]", "runtime.any.System.Globalization.Calendars": "(, 4.3.0]", "runtime.any.System.IO": "(, 4.3.0]", "runtime.any.System.Reflection": "(, 4.3.0]", "runtime.any.System.Reflection.Extensions": "(, 4.3.0]", "runtime.any.System.Reflection.Primitives": "(, 4.3.0]", "runtime.any.System.Resources.ResourceManager": "(, 4.3.0]", "runtime.any.System.Runtime": "(, 4.3.1]", "runtime.any.System.Runtime.Handles": "(, 4.3.0]", "runtime.any.System.Runtime.InteropServices": "(, 4.3.0]", "runtime.any.System.Text.Encoding": "(, 4.3.0]", "runtime.any.System.Text.Encoding.Extensions": "(, 4.3.0]", "runtime.any.System.Threading.Tasks": "(, 4.3.0]", "runtime.any.System.Threading.Timer": "(, 4.3.0]", "runtime.aot.System.Collections": "(, 4.3.0]", "runtime.aot.System.Diagnostics.Tools": "(, 4.3.0]", "runtime.aot.System.Diagnostics.Tracing": "(, 4.3.0]", "runtime.aot.System.Globalization": "(, 4.3.0]", "runtime.aot.System.Globalization.Calendars": "(, 4.3.0]", "runtime.aot.System.IO": "(, 4.3.0]", "runtime.aot.System.Reflection": "(, 4.3.0]", "runtime.aot.System.Reflection.Extensions": "(, 4.3.0]", "runtime.aot.System.Reflection.Primitives": "(, 4.3.0]", "runtime.aot.System.Resources.ResourceManager": "(, 4.3.0]", "runtime.aot.System.Runtime": "(, 4.3.1]", "runtime.aot.System.Runtime.Handles": "(, 4.3.0]", "runtime.aot.System.Runtime.InteropServices": "(, 4.3.0]", "runtime.aot.System.Text.Encoding": "(, 4.3.0]", "runtime.aot.System.Text.Encoding.Extensions": "(, 4.3.0]", "runtime.aot.System.Threading.Tasks": "(, 4.3.0]", "runtime.aot.System.Threading.Timer": "(, 4.3.0]", "runtime.debian.8-x64.runtime.native.System": "(, 4.3.1]", "runtime.debian.8-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.debian.8-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.debian.8-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.debian.9-x64.runtime.native.System": "(, 4.3.1]", "runtime.debian.9-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.debian.9-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.debian.9-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.fedora.23-x64.runtime.native.System": "(, 4.3.1]", "runtime.fedora.23-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.fedora.23-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.fedora.23-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.fedora.24-x64.runtime.native.System": "(, 4.3.1]", "runtime.fedora.24-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.fedora.24-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.fedora.24-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.fedora.27-x64.runtime.native.System": "(, 4.3.1]", "runtime.fedora.27-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.fedora.27-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.fedora.27-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.fedora.28-x64.runtime.native.System": "(, 4.3.1]", "runtime.fedora.28-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.fedora.28-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.fedora.28-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.native.System.Security.Cryptography.Apple": "(, 4.3.1]", "runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.opensuse.13.2-x64.runtime.native.System": "(, 4.3.1]", "runtime.opensuse.13.2-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.opensuse.42.1-x64.runtime.native.System": "(, 4.3.1]", "runtime.opensuse.42.1-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.opensuse.42.3-x64.runtime.native.System": "(, 4.3.1]", "runtime.opensuse.42.3-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.osx.10.10-x64.runtime.native.System": "(, 4.3.1]", "runtime.osx.10.10-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.osx.10.10-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.osx.10.10-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "(, 4.3.1]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.rhel.7-x64.runtime.native.System": "(, 4.3.1]", "runtime.rhel.7-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.rhel.7-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.rhel.7-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.ubuntu.14.04-x64.runtime.native.System": "(, 4.3.1]", "runtime.ubuntu.14.04-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.ubuntu.16.04-x64.runtime.native.System": "(, 4.3.1]", "runtime.ubuntu.16.04-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.ubuntu.16.10-x64.runtime.native.System": "(, 4.3.1]", "runtime.ubuntu.16.10-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.ubuntu.18.04-x64.runtime.native.System": "(, 4.3.1]", "runtime.ubuntu.18.04-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.unix.Microsoft.Win32.Primitives": "(, 4.3.0]", "runtime.unix.System.Console": "(, 4.3.1]", "runtime.unix.System.Diagnostics.Debug": "(, 4.3.0]", "runtime.unix.System.IO.FileSystem": "(, 4.3.0]", "runtime.unix.System.Net.Primitives": "(, 4.3.0]", "runtime.unix.System.Net.Sockets": "(, 4.3.0]", "runtime.unix.System.Private.Uri": "(, 4.3.1]", "runtime.unix.System.Runtime.Extensions": "(, 4.3.1]", "runtime.win.Microsoft.Win32.Primitives": "(, 4.3.0]", "runtime.win.System.Console": "(, 4.3.1]", "runtime.win.System.Diagnostics.Debug": "(, 4.3.0]", "runtime.win.System.IO.FileSystem": "(, 4.3.0]", "runtime.win.System.Net.Primitives": "(, 4.3.0]", "runtime.win.System.Net.Sockets": "(, 4.3.0]", "runtime.win.System.Runtime.Extensions": "(, 4.3.1]", "runtime.win10-arm-aot.runtime.native.System.IO.Compression": "(, 4.0.1]", "runtime.win10-arm64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.win10-x64-aot.runtime.native.System.IO.Compression": "(, 4.0.1]", "runtime.win10-x86-aot.runtime.native.System.IO.Compression": "(, 4.0.1]", "runtime.win7-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.win7-x86.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.win7.System.Private.Uri": "(, 4.3.1]", "runtime.win8-arm.runtime.native.System.IO.Compression": "(, 4.3.2]", "System.AppContext": "(, 4.3.0]", "System.Buffers": "(, 4.5.1]", "System.Collections": "(, 4.3.0]", "System.Collections.Concurrent": "(, 4.3.0]", "System.Collections.Immutable": "(, 8.0.0]", "System.Collections.NonGeneric": "(, 4.3.0]", "System.Collections.Specialized": "(, 4.3.0]", "System.ComponentModel": "(, 4.3.0]", "System.ComponentModel.Annotations": "(, 5.0.0]", "System.ComponentModel.EventBasedAsync": "(, 4.3.0]", "System.ComponentModel.Primitives": "(, 4.3.0]", "System.ComponentModel.TypeConverter": "(, 4.3.0]", "System.Console": "(, 4.3.1]", "System.Data.Common": "(, 4.3.0]", "System.Data.DataSetExtensions": "(, 4.5.0]", "System.Diagnostics.Contracts": "(, 4.3.0]", "System.Diagnostics.Debug": "(, 4.3.0]", "System.Diagnostics.DiagnosticSource": "(, 8.0.1]", "System.Diagnostics.EventLog": "(, 8.0.0]", "System.Diagnostics.FileVersionInfo": "(, 4.3.0]", "System.Diagnostics.Process": "(, 4.3.0]", "System.Diagnostics.StackTrace": "(, 4.3.0]", "System.Diagnostics.TextWriterTraceListener": "(, 4.3.0]", "System.Diagnostics.Tools": "(, 4.3.0]", "System.Diagnostics.TraceSource": "(, 4.3.0]", "System.Diagnostics.Tracing": "(, 4.3.0]", "System.Drawing.Common": "(, 5.0.0]", "System.Drawing.Primitives": "(, 4.3.0]", "System.Dynamic.Runtime": "(, 4.3.0]", "System.Formats.Asn1": "(, 8.0.1]", "System.Globalization": "(, 4.3.0]", "System.Globalization.Calendars": "(, 4.3.0]", "System.Globalization.Extensions": "(, 4.3.0]", "System.IO": "(, 4.3.0]", "System.IO.Compression": "(, 4.3.0]", "System.IO.Compression.ZipFile": "(, 4.3.0]", "System.IO.FileSystem": "(, 4.3.0]", "System.IO.FileSystem.AccessControl": "(, 5.0.0]", "System.IO.FileSystem.DriveInfo": "(, 4.3.1]", "System.IO.FileSystem.Primitives": "(, 4.3.0]", "System.IO.FileSystem.Watcher": "(, 4.3.0]", "System.IO.IsolatedStorage": "(, 4.3.0]", "System.IO.MemoryMappedFiles": "(, 4.3.0]", "System.IO.Pipelines": "(, 8.0.0]", "System.IO.Pipes": "(, 4.3.0]", "System.IO.Pipes.AccessControl": "(, 4.6.0]", "System.IO.UnmanagedMemoryStream": "(, 4.3.0]", "System.Linq": "(, 4.3.0]", "System.Linq.Expressions": "(, 4.3.0]", "System.Linq.Parallel": "(, 4.3.0]", "System.Linq.Queryable": "(, 4.3.0]", "System.Memory": "(, 4.5.5]", "System.Net.Http": "(, 4.3.4]", "System.Net.Http.Json": "(, 8.0.1]", "System.Net.NameResolution": "(, 4.3.0]", "System.Net.NetworkInformation": "(, 4.3.0]", "System.Net.Ping": "(, 4.3.0]", "System.Net.Primitives": "(, 4.3.1]", "System.Net.Requests": "(, 4.3.0]", "System.Net.Security": "(, 4.3.2]", "System.Net.Sockets": "(, 4.3.0]", "System.Net.WebHeaderCollection": "(, 4.3.0]", "System.Net.WebSockets": "(, 4.3.0]", "System.Net.WebSockets.Client": "(, 4.3.2]", "System.Numerics.Vectors": "(, 4.5.0]", "System.ObjectModel": "(, 4.3.0]", "System.Private.DataContractSerialization": "(, 4.3.0]", "System.Private.Uri": "(, 4.3.0]", "System.Reflection": "(, 4.3.0]", "System.Reflection.DispatchProxy": "(, 4.7.1]", "System.Reflection.Emit": "(, 4.7.0]", "System.Reflection.Emit.ILGeneration": "(, 4.7.0]", "System.Reflection.Emit.Lightweight": "(, 4.7.0]", "System.Reflection.Extensions": "(, 4.3.0]", "System.Reflection.Metadata": "(, 8.0.1]", "System.Reflection.Primitives": "(, 4.3.0]", "System.Reflection.TypeExtensions": "(, 4.7.0]", "System.Resources.Reader": "(, 4.3.0]", "System.Resources.ResourceManager": "(, 4.3.0]", "System.Resources.Writer": "(, 4.3.0]", "System.Runtime": "(, 4.3.1]", "System.Runtime.CompilerServices.Unsafe": "(, 6.0.0]", "System.Runtime.CompilerServices.VisualC": "(, 4.3.0]", "System.Runtime.Extensions": "(, 4.3.1]", "System.Runtime.Handles": "(, 4.3.0]", "System.Runtime.InteropServices": "(, 4.3.0]", "System.Runtime.InteropServices.RuntimeInformation": "(, 4.3.0]", "System.Runtime.InteropServices.WindowsRuntime": "(, 4.3.0]", "System.Runtime.Loader": "(, 4.3.0]", "System.Runtime.Numerics": "(, 4.3.0]", "System.Runtime.Serialization.Formatters": "(, 4.3.0]", "System.Runtime.Serialization.Json": "(, 4.3.0]", "System.Runtime.Serialization.Primitives": "(, 4.3.0]", "System.Runtime.Serialization.Xml": "(, 4.3.0]", "System.Runtime.WindowsRuntime": "(, 4.7.0]", "System.Runtime.WindowsRuntime.UI.Xaml": "(, 4.7.0]", "System.Security.AccessControl": "(, 6.0.1]", "System.Security.Claims": "(, 4.3.0]", "System.Security.Cryptography.Algorithms": "(, 4.3.1]", "System.Security.Cryptography.Cng": "(, 5.0.0]", "System.Security.Cryptography.Csp": "(, 4.3.0]", "System.Security.Cryptography.Encoding": "(, 4.3.0]", "System.Security.Cryptography.OpenSsl": "(, 5.0.0]", "System.Security.Cryptography.Pkcs": "(, 8.0.0]", "System.Security.Cryptography.Primitives": "(, 4.3.0]", "System.Security.Cryptography.X509Certificates": "(, 4.3.2]", "System.Security.Cryptography.Xml": "(, 8.0.0]", "System.Security.Permissions": "(, 5.0.0]", "System.Security.Principal": "(, 4.3.0]", "System.Security.Principal.Windows": "(, 5.0.0]", "System.Security.SecureString": "(, 4.3.0]", "System.Text.Encoding": "(, 4.3.0]", "System.Text.Encoding.CodePages": "(, 8.0.0]", "System.Text.Encoding.Extensions": "(, 4.3.0]", "System.Text.Encodings.Web": "(, 8.0.0]", "System.Text.Json": "(, 8.0.5]", "System.Text.RegularExpressions": "(, 4.3.1]", "System.Threading": "(, 4.3.0]", "System.Threading.Channels": "(, 8.0.0]", "System.Threading.Overlapped": "(, 4.3.0]", "System.Threading.RateLimiting": "(, 8.0.0]", "System.Threading.Tasks": "(, 4.3.0]", "System.Threading.Tasks.Dataflow": "(, 8.0.1]", "System.Threading.Tasks.Extensions": "(, 4.5.4]", "System.Threading.Tasks.Parallel": "(, 4.3.0]", "System.Threading.Thread": "(, 4.3.0]", "System.Threading.ThreadPool": "(, 4.3.0]", "System.Threading.Timer": "(, 4.3.0]", "System.ValueTuple": "(, 4.5.0]", "System.Windows.Extensions": "(, 5.0.0]", "System.Xml.ReaderWriter": "(, 4.3.1]", "System.Xml.XDocument": "(, 4.3.0]", "System.Xml.XmlDocument": "(, 4.3.0]", "System.Xml.XmlSerializer": "(, 4.3.0]", "System.Xml.XPath": "(, 4.3.0]", "System.Xml.XPath.XDocument": "(, 4.3.0]"}}}}}