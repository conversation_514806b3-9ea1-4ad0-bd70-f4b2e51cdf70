{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"Migrators/1.0.0": {"dependencies": {"Infrastructure": "1.0.0", "Microsoft.EntityFrameworkCore.Design": "8.0.10", "Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Configuration.Json": "9.0.1"}, "runtime": {"Migrators.dll": {}}}, "Amazon.Extensions.CognitoAuthentication/2.5.5": {"dependencies": {"AWSSDK.CognitoIdentity": "3.7.300.74", "AWSSDK.CognitoIdentityProvider": "3.7.303.19"}, "runtime": {"lib/netstandard2.0/Amazon.Extensions.CognitoAuthentication.dll": {"assemblyVersion": "*******", "fileVersion": "2.5.3.0"}}}, "Ardalis.Specification/8.0.0": {"runtime": {"lib/net8.0/Ardalis.Specification.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Ardalis.Specification.EntityFrameworkCore/8.0.0": {"dependencies": {"Ardalis.Specification": "8.0.0", "Microsoft.EntityFrameworkCore": "9.0.5", "Microsoft.EntityFrameworkCore.Relational": "9.0.1"}, "runtime": {"lib/net8.0/Ardalis.Specification.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Asp.Versioning.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net8.0/Asp.Versioning.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.8742.36015"}}}, "Asp.Versioning.Http/8.0.0": {"dependencies": {"Asp.Versioning.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Asp.Versioning.Http.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.8742.36024"}}}, "Asp.Versioning.Mvc/8.0.0": {"dependencies": {"Asp.Versioning.Http": "8.0.0"}, "runtime": {"lib/net8.0/Asp.Versioning.Mvc.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.8742.36024"}}}, "AWS.Logger.Core/3.1.0": {"dependencies": {"AWSSDK.CloudWatchLogs": "3.7.0.5"}, "runtime": {"lib/netstandard2.0/AWS.Logger.Core.dll": {"assemblyVersion": "3.1.0.0", "fileVersion": "3.1.0.0"}}}, "AWS.Logger.SeriLog/3.2.0": {"dependencies": {"AWS.Logger.Core": "3.1.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Serilog": "3.1.1"}, "runtime": {"lib/netstandard2.0/AWS.Logger.SeriLog.dll": {"assemblyVersion": "3.2.0.0", "fileVersion": "3.2.0.0"}}}, "AWSSDK.CloudWatchLogs/3.7.0.5": {"dependencies": {"AWSSDK.Core": "3.7.400.40"}, "runtime": {"lib/netcoreapp3.1/AWSSDK.CloudWatchLogs.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.7.0.5"}}}, "AWSSDK.CognitoIdentity/3.7.300.74": {"dependencies": {"AWSSDK.Core": "3.7.400.40", "AWSSDK.SecurityToken": "3.7.300.75"}, "runtime": {"lib/net8.0/AWSSDK.CognitoIdentity.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.7.300.74"}}}, "AWSSDK.CognitoIdentityProvider/3.7.303.19": {"dependencies": {"AWSSDK.Core": "3.7.400.40"}, "runtime": {"lib/net8.0/AWSSDK.CognitoIdentityProvider.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.7.303.19"}}}, "AWSSDK.Core/3.7.400.40": {"runtime": {"lib/net8.0/AWSSDK.Core.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.7.400.40"}}}, "AWSSDK.S3/3.7.405.4": {"dependencies": {"AWSSDK.Core": "3.7.400.40"}, "runtime": {"lib/net8.0/AWSSDK.S3.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.7.405.4"}}}, "AWSSDK.SecurityToken/3.7.300.75": {"dependencies": {"AWSSDK.Core": "3.7.400.40"}, "runtime": {"lib/net8.0/AWSSDK.SecurityToken.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.7.300.75"}}}, "Azure.Core/1.38.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "System.ClientModel": "1.0.0", "System.Memory.Data": "1.0.2"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.38.0.0", "fileVersion": "1.3800.24.12602"}}}, "Azure.Identity/1.11.4": {"dependencies": {"Azure.Core": "1.38.0", "Microsoft.Identity.Client": "4.65.0", "Microsoft.Identity.Client.Extensions.Msal": "4.61.3", "System.Security.Cryptography.ProtectedData": "4.7.0"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.11.4.0", "fileVersion": "1.1100.424.31005"}}}, "Azure.Security.KeyVault.Certificates/4.6.0": {"dependencies": {"Azure.Core": "1.38.0"}, "runtime": {"lib/netstandard2.0/Azure.Security.KeyVault.Certificates.dll": {"assemblyVersion": "4.6.0.0", "fileVersion": "4.600.24.11403"}}}, "Azure.Security.KeyVault.Secrets/4.6.0": {"dependencies": {"Azure.Core": "1.38.0"}, "runtime": {"lib/netstandard2.0/Azure.Security.KeyVault.Secrets.dll": {"assemblyVersion": "4.6.0.0", "fileVersion": "4.600.24.11403"}}}, "Dapper/2.1.35": {"runtime": {"lib/net7.0/Dapper.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.35.13827"}}}, "DocumentFormat.OpenXml/2.16.0": {"dependencies": {"System.IO.Packaging": "4.7.0"}, "runtime": {"lib/netstandard2.0/DocumentFormat.OpenXml.dll": {"assemblyVersion": "2.16.0.0", "fileVersion": "2.16.0.0"}}}, "EPPlus/7.6.0": {"dependencies": {"EPPlus.System.Drawing": "7.5.0", "Microsoft.Extensions.Configuration.Json": "9.0.1", "Microsoft.IO.RecyclableMemoryStream": "3.0.1", "System.Security.Cryptography.Pkcs": "9.0.1", "System.Text.Encoding.CodePages": "9.0.1"}, "runtime": {"lib/net8.0/EPPlus.dll": {"assemblyVersion": "7.6.0.0", "fileVersion": "7.6.0.0"}}}, "EPPlus.Interfaces/7.5.0": {"runtime": {"lib/net8.0/EPPlus.Interfaces.dll": {"assemblyVersion": "7.5.0.0", "fileVersion": "7.5.0.0"}}}, "EPPlus.System.Drawing/7.5.0": {"dependencies": {"EPPlus.Interfaces": "7.5.0", "System.Drawing.Common": "8.0.4"}, "runtime": {"lib/net8.0/EPPlus.System.Drawing.dll": {"assemblyVersion": "7.5.0.0", "fileVersion": "7.5.0.0"}}}, "Finbuckle.MultiTenant/6.13.1": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.5", "Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.DependencyInjection": "9.0.5", "Microsoft.Extensions.Http": "8.0.0", "Microsoft.Extensions.Logging": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}, "runtime": {"lib/net8.0/Finbuckle.MultiTenant.dll": {"assemblyVersion": "6.13.1.0", "fileVersion": "6.13.1.0"}}}, "Finbuckle.MultiTenant.AspNetCore/6.13.1": {"dependencies": {"Finbuckle.MultiTenant": "6.13.1", "Microsoft.AspNetCore.Authentication.OpenIdConnect": "8.0.1"}, "runtime": {"lib/net8.0/Finbuckle.MultiTenant.AspNetCore.dll": {"assemblyVersion": "6.13.1.0", "fileVersion": "6.13.1.0"}}}, "Finbuckle.MultiTenant.EntityFrameworkCore/6.13.1": {"dependencies": {"Finbuckle.MultiTenant": "6.13.1", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "8.0.10", "Microsoft.EntityFrameworkCore.Relational": "9.0.1", "Microsoft.Extensions.Identity.Stores": "8.0.10"}, "runtime": {"lib/net8.0/Finbuckle.MultiTenant.EntityFrameworkCore.dll": {"assemblyVersion": "6.13.1.0", "fileVersion": "6.13.1.0"}}}, "FluentValidation/11.9.0": {"runtime": {"lib/net8.0/FluentValidation.dll": {"assemblyVersion": "1*******", "fileVersion": "11.9.0.0"}}}, "FluentValidation.DependencyInjectionExtensions/11.9.0": {"dependencies": {"FluentValidation": "11.9.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5"}, "runtime": {"lib/netstandard2.1/FluentValidation.DependencyInjectionExtensions.dll": {"assemblyVersion": "1*******", "fileVersion": "11.9.0.0"}}}, "Hangfire/1.8.15": {"dependencies": {"Hangfire.AspNetCore": "1.8.15", "Hangfire.Core": "1.8.15", "Hangfire.SqlServer": "1.8.15"}}, "Hangfire.AspNetCore/1.8.15": {"dependencies": {"Hangfire.NetCore": "1.8.15"}, "runtime": {"lib/netcoreapp3.0/Hangfire.AspNetCore.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Hangfire.Console/1.4.2": {"dependencies": {"Hangfire.Core": "1.8.15", "NETStandard.Library": "1.6.1"}, "runtime": {"lib/netstandard1.3/Hangfire.Console.dll": {"assemblyVersion": "1.4.2.0", "fileVersion": "1.4.2.0"}}}, "Hangfire.Console.Extensions/1.0.5": {"dependencies": {"Hangfire.Console": "1.4.2", "Microsoft.Extensions.Logging": "9.0.5", "Microsoft.Extensions.Logging.Configuration": "5.0.0"}, "runtime": {"lib/netstandard2.0/Hangfire.Console.Extensions.dll": {"assemblyVersion": "1.0.5.0", "fileVersion": "1.0.5.0"}}}, "Hangfire.Core/1.8.15": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Hangfire.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "resources": {"lib/netstandard2.0/ca/Hangfire.Core.resources.dll": {"locale": "ca"}, "lib/netstandard2.0/de/Hangfire.Core.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/Hangfire.Core.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fa/Hangfire.Core.resources.dll": {"locale": "fa"}, "lib/netstandard2.0/fr/Hangfire.Core.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/nb/Hangfire.Core.resources.dll": {"locale": "nb"}, "lib/netstandard2.0/nl/Hangfire.Core.resources.dll": {"locale": "nl"}, "lib/netstandard2.0/pt-BR/Hangfire.Core.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/pt-PT/Hangfire.Core.resources.dll": {"locale": "pt-PT"}, "lib/netstandard2.0/pt/Hangfire.Core.resources.dll": {"locale": "pt"}, "lib/netstandard2.0/sv/Hangfire.Core.resources.dll": {"locale": "sv"}, "lib/netstandard2.0/tr-TR/Hangfire.Core.resources.dll": {"locale": "tr-TR"}, "lib/netstandard2.0/zh-TW/Hangfire.Core.resources.dll": {"locale": "zh-TW"}, "lib/netstandard2.0/zh/Hangfire.Core.resources.dll": {"locale": "zh"}}}, "Hangfire.Dashboard.Basic.Authentication/5.0.0": {"dependencies": {"Hangfire.AspNetCore": "1.8.15", "Hangfire.Core": "1.8.15", "Microsoft.AspNetCore.Http": "2.2.2", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net5.0/HangfireBasicAuthenticationFilter.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Hangfire.NetCore/1.8.15": {"dependencies": {"Hangfire.Core": "1.8.15", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.5"}, "runtime": {"lib/netstandard2.1/Hangfire.NetCore.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Hangfire.PostgreSql/1.20.10": {"dependencies": {"Dapper": "2.1.35", "Hangfire.Core": "1.8.15", "Npgsql": "9.0.3"}, "runtime": {"lib/netstandard2.0/Hangfire.PostgreSql.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Hangfire.SqlServer/1.8.15": {"dependencies": {"Hangfire.Core": "1.8.15"}, "runtime": {"lib/netstandard2.0/Hangfire.SqlServer.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "********", "fileVersion": "2.14.1.48190"}}}, "Mapster/7.4.0": {"dependencies": {"Mapster.Core": "1.2.1"}, "runtime": {"lib/net7.0/Mapster.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Mapster.Core/1.2.1": {"runtime": {"lib/net7.0/Mapster.Core.dll": {"assemblyVersion": "1.2.1.0", "fileVersion": "1.2.1.0"}}}, "Mapster.DependencyInjection/1.0.1": {"dependencies": {"Mapster": "7.4.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5"}, "runtime": {"lib/net7.0/Mapster.DependencyInjection.dll": {"assemblyVersion": "1.0.1.0", "fileVersion": "1.0.1.0"}}}, "MediatR/12.2.0": {"dependencies": {"MediatR.Contracts": "2.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5"}, "runtime": {"lib/net6.0/MediatR.dll": {"assemblyVersion": "1*******", "fileVersion": "12.2.0.0"}}}, "MediatR.Contracts/2.0.1": {"runtime": {"lib/netstandard2.0/MediatR.Contracts.dll": {"assemblyVersion": "2.0.1.0", "fileVersion": "2.0.1.0"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.10": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.1.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1024.46804"}}}, "Microsoft.AspNetCore.Authentication.OpenIdConnect/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.1.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authentication.OpenIdConnect.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.123.58008"}}}, "Microsoft.AspNetCore.Connections.Abstractions/8.0.10": {"dependencies": {"Microsoft.Extensions.Features": "8.0.10", "System.IO.Pipelines": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Connections.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46804"}}}, "Microsoft.AspNetCore.Cryptography.Internal/8.0.10": {"runtime": {"lib/net8.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46804"}}}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/8.0.10": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "8.0.10"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46804"}}}, "Microsoft.AspNetCore.DataProtection/8.0.1": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "8.0.10", "Microsoft.AspNetCore.DataProtection.Abstractions": "8.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "System.Security.Cryptography.Xml": "8.0.1"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.DataProtection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.123.58008"}}}, "Microsoft.AspNetCore.DataProtection.Abstractions/8.0.1": {"runtime": {"lib/net8.0/Microsoft.AspNetCore.DataProtection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.123.58008"}}}, "Microsoft.AspNetCore.Http/2.2.2": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "9.0.5", "Microsoft.Net.Http.Headers": "2.2.0"}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0"}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.5"}}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/8.0.10": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "9.0.1", "Microsoft.Extensions.Identity.Stores": "8.0.10"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Identity.EntityFrameworkCore.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1024.46804"}}}, "Microsoft.AspNetCore.Mvc.Versioning/5.1.0": {"runtime": {"lib/net6.0/Microsoft.AspNetCore.Mvc.Versioning.dll": {"assemblyVersion": "5.1.0.0", "fileVersion": "5.1.8270.41513"}}}, "Microsoft.AspNetCore.SignalR.Client.Core/8.0.10": {"dependencies": {"Microsoft.AspNetCore.SignalR.Common": "8.0.10", "Microsoft.AspNetCore.SignalR.Protocols.Json": "8.0.10", "Microsoft.Extensions.DependencyInjection": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.SignalR.Client.Core.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1024.46804"}}}, "Microsoft.AspNetCore.SignalR.Common/8.0.10": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "8.0.10", "Microsoft.Extensions.Options": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.SignalR.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46804"}}}, "Microsoft.AspNetCore.SignalR.Protocols.Json/8.0.10": {"dependencies": {"Microsoft.AspNetCore.SignalR.Common": "8.0.10"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.SignalR.Protocols.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46804"}}}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"dependencies": {"Microsoft.Net.Http.Headers": "2.2.0"}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Bcl.Cryptography/9.0.1": {"dependencies": {"System.Formats.Asn1": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Bcl.Cryptography.dll": {"assemblyVersion": "9.0.0.1", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Bcl.TimeProvider/8.0.1": {"runtime": {"lib/net8.0/Microsoft.Bcl.TimeProvider.dll": {"assemblyVersion": "8.0.0.1", "fileVersion": "8.0.123.58001"}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.3": {}, "Microsoft.CodeAnalysis.Common/4.5.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.3"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "*******", "fileVersion": "4.500.23.10905"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.5.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.5.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "4.500.23.10905"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.5.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "4.5.0", "Microsoft.CodeAnalysis.Common": "4.5.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.5.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.500.23.10905"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.5.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "6.0.0", "Microsoft.CodeAnalysis.Common": "4.5.0", "System.Composition": "6.0.0", "System.IO.Pipelines": "9.0.1"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.500.23.10905"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.EntityFrameworkCore/9.0.5": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.5", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.5", "Microsoft.Extensions.Caching.Memory": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "9.0.5.0", "fileVersion": "9.0.525.21604"}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.5": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "9.0.5.0", "fileVersion": "9.0.525.21604"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.5": {}, "Microsoft.EntityFrameworkCore.Design/8.0.10": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.5.0", "Microsoft.EntityFrameworkCore.Relational": "9.0.1", "Microsoft.Extensions.DependencyModel": "8.0.2", "Mono.TextTemplating": "2.2.1"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1024.46708"}}}, "Microsoft.EntityFrameworkCore.Relational/9.0.1": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.5", "Microsoft.Extensions.Caching.Memory": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.Logging": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61002"}}}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {}, "Microsoft.Extensions.Caching.Abstractions/9.0.5": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Caching.Memory/9.0.5": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Configuration/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.1": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.1"}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.1", "Microsoft.Extensions.FileProviders.Physical": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Configuration.Json/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.1", "System.Text.Json": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.DependencyInjection/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.5": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.DependencyModel/8.0.2": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "8.0.0.2", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Diagnostics/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5"}}, "Microsoft.Extensions.Features/8.0.10": {"runtime": {"lib/net8.0/Microsoft.Extensions.Features.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46804"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.1": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.FileProviders.Embedded/8.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.1"}}, "Microsoft.Extensions.FileProviders.Physical/9.0.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.1", "Microsoft.Extensions.FileSystemGlobbing": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.1": {"runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.5"}}, "Microsoft.Extensions.Http/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Diagnostics": "8.0.0", "Microsoft.Extensions.Logging": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5"}}, "Microsoft.Extensions.Identity.Core/8.0.10": {"dependencies": {"Microsoft.AspNetCore.Cryptography.KeyDerivation": "8.0.10", "Microsoft.Extensions.Logging": "9.0.5", "Microsoft.Extensions.Options": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Identity.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46804"}}}, "Microsoft.Extensions.Identity.Stores/8.0.10": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.5", "Microsoft.Extensions.Identity.Core": "8.0.10", "Microsoft.Extensions.Logging": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Identity.Stores.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46804"}}}, "Microsoft.Extensions.Logging/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "System.Diagnostics.DiagnosticSource": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Logging.Configuration/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Microsoft.Extensions.ObjectPool/2.2.0": {}, "Microsoft.Extensions.Options/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}}, "Microsoft.Extensions.Primitives/9.0.5": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Identity.Abstractions/7.1.0": {"runtime": {"lib/net8.0/Microsoft.Identity.Abstractions.dll": {"assemblyVersion": "7.1.0.0", "fileVersion": "7.1.0.0"}}}, "Microsoft.Identity.Client/4.65.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.1.2"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.65.0.0", "fileVersion": "4.65.0.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"dependencies": {"Microsoft.Identity.Client": "4.65.0", "System.Security.Cryptography.ProtectedData": "4.7.0"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "4.61.3.0", "fileVersion": "4.61.3.0"}}}, "Microsoft.Identity.Web/3.2.2": {"dependencies": {"Microsoft.Extensions.Http": "8.0.0", "Microsoft.Identity.Web.Certificate": "3.2.2", "Microsoft.Identity.Web.Certificateless": "3.2.2", "Microsoft.Identity.Web.TokenAcquisition": "3.2.2", "Microsoft.Identity.Web.TokenCache": "3.2.2", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.1.2", "Microsoft.IdentityModel.Validators": "8.1.2", "System.IdentityModel.Tokens.Jwt": "8.1.2"}, "runtime": {"lib/net8.0/Microsoft.Identity.Web.dll": {"assemblyVersion": "3.2.2.0", "fileVersion": "3.2.2.0"}}}, "Microsoft.Identity.Web.Certificate/3.2.2": {"dependencies": {"Azure.Identity": "1.11.4", "Azure.Security.KeyVault.Certificates": "4.6.0", "Azure.Security.KeyVault.Secrets": "4.6.0", "Microsoft.Identity.Abstractions": "7.1.0", "Microsoft.Identity.Web.Certificateless": "3.2.2", "Microsoft.Identity.Web.Diagnostics": "3.2.2"}, "runtime": {"lib/net8.0/Microsoft.Identity.Web.Certificate.dll": {"assemblyVersion": "3.2.2.0", "fileVersion": "3.2.2.0"}}}, "Microsoft.Identity.Web.Certificateless/3.2.2": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Identity.Client": "4.65.0", "Microsoft.IdentityModel.JsonWebTokens": "8.1.2"}, "runtime": {"lib/netstandard2.0/Microsoft.Identity.Web.Certificateless.dll": {"assemblyVersion": "3.2.2.0", "fileVersion": "3.2.2.0"}}}, "Microsoft.Identity.Web.Diagnostics/3.2.2": {"runtime": {"lib/net8.0/Microsoft.Identity.Web.Diagnostics.dll": {"assemblyVersion": "3.2.2.0", "fileVersion": "3.2.2.0"}}}, "Microsoft.Identity.Web.TokenAcquisition/3.2.2": {"dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": "8.0.10", "Microsoft.AspNetCore.Authentication.OpenIdConnect": "8.0.1", "Microsoft.Extensions.Http": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0", "Microsoft.Identity.Abstractions": "7.1.0", "Microsoft.Identity.Web.Certificate": "3.2.2", "Microsoft.Identity.Web.Certificateless": "3.2.2", "Microsoft.Identity.Web.TokenCache": "3.2.2", "Microsoft.IdentityModel.Logging": "8.1.2", "Microsoft.IdentityModel.LoggingExtensions": "8.1.2", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.1.2", "System.IdentityModel.Tokens.Jwt": "8.1.2"}, "runtime": {"lib/net8.0/Microsoft.Identity.Web.TokenAcquisition.dll": {"assemblyVersion": "3.2.2.0", "fileVersion": "3.2.2.0"}}}, "Microsoft.Identity.Web.TokenCache/3.2.2": {"dependencies": {"Microsoft.AspNetCore.DataProtection": "8.0.1", "Microsoft.Extensions.Caching.Memory": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "Microsoft.Identity.Client": "4.65.0", "Microsoft.Identity.Web.Diagnostics": "3.2.2", "System.Security.Cryptography.Pkcs": "9.0.1", "System.Security.Cryptography.Xml": "8.0.1"}, "runtime": {"lib/net8.0/Microsoft.Identity.Web.TokenCache.dll": {"assemblyVersion": "3.2.2.0", "fileVersion": "3.2.2.0"}}}, "Microsoft.IdentityModel.Abstractions/8.1.2": {"runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.2.51008"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.1.2": {"dependencies": {"Microsoft.Bcl.TimeProvider": "8.0.1", "Microsoft.IdentityModel.Tokens": "8.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.2.51008"}}}, "Microsoft.IdentityModel.Logging/8.1.2": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.2.51008"}}}, "Microsoft.IdentityModel.LoggingExtensions/8.1.2": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.IdentityModel.Abstractions": "8.1.2"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.LoggingExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.2.51008"}}}, "Microsoft.IdentityModel.Protocols/8.1.2": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.2.51008"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.1.2": {"dependencies": {"Microsoft.IdentityModel.Protocols": "8.1.2", "System.IdentityModel.Tokens.Jwt": "8.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.2.51008"}}}, "Microsoft.IdentityModel.Tokens/8.1.2": {"dependencies": {"Microsoft.Bcl.TimeProvider": "8.0.1", "Microsoft.IdentityModel.Logging": "8.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.2.51008"}}}, "Microsoft.IdentityModel.Validators/8.1.2": {"dependencies": {"Microsoft.IdentityModel.Protocols": "8.1.2", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.1.2", "Microsoft.IdentityModel.Tokens": "8.1.2", "System.IdentityModel.Tokens.Jwt": "8.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Validators.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.2.51008"}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.1": {"runtime": {"lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll": {"assemblyVersion": "3.0.1.0", "fileVersion": "3.0.1.0"}}}, "Microsoft.Net.Http.Headers/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.5"}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.OpenApi/1.6.23": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "1.6.23.0", "fileVersion": "1.6.23.0"}}}, "Microsoft.Win32.SystemEvents/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Mono.TextTemplating/2.2.1": {"dependencies": {"System.CodeDom": "4.4.0"}, "runtime": {"lib/netstandard2.0/Mono.TextTemplating.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.1.1"}}}, "Namotion.Reflection/3.1.1": {"runtime": {"lib/netstandard2.0/Namotion.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NETStandard.Library/1.6.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0"}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "NJsonSchema/11.0.0": {"dependencies": {"NJsonSchema.Annotations": "11.0.0", "Namotion.Reflection": "3.1.1", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net6.0/NJsonSchema.dll": {"assemblyVersion": "1*******", "fileVersion": "1*******"}}}, "NJsonSchema.Annotations/11.0.0": {"runtime": {"lib/netstandard2.0/NJsonSchema.Annotations.dll": {"assemblyVersion": "1*******", "fileVersion": "1*******"}}}, "NJsonSchema.NewtonsoftJson/11.0.0": {"dependencies": {"NJsonSchema": "11.0.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/NJsonSchema.NewtonsoftJson.dll": {"assemblyVersion": "1*******", "fileVersion": "1*******"}}}, "NJsonSchema.Yaml/11.0.0": {"dependencies": {"NJsonSchema": "11.0.0", "YamlDotNet": "13.7.1"}, "runtime": {"lib/netstandard2.0/NJsonSchema.Yaml.dll": {"assemblyVersion": "1*******", "fileVersion": "1*******"}}}, "Npgsql/9.0.3": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.5"}, "runtime": {"lib/net8.0/Npgsql.dll": {"assemblyVersion": "9.0.3.0", "fileVersion": "9.0.3.0"}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/9.0.4": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.5", "Microsoft.EntityFrameworkCore.Relational": "9.0.1", "Npgsql": "9.0.3"}, "runtime": {"lib/net8.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NSwag.Annotations/14.0.3": {"runtime": {"lib/netstandard2.0/NSwag.Annotations.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "NSwag.AspNetCore/14.0.3": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Microsoft.Extensions.FileProviders.Embedded": "8.0.0", "NSwag.Annotations": "14.0.3", "NSwag.Core": "14.0.3", "NSwag.Core.Yaml": "14.0.3", "NSwag.Generation": "14.0.3", "NSwag.Generation.AspNetCore": "14.0.3"}, "runtime": {"lib/net8.0/NSwag.AspNetCore.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "NSwag.Core/14.0.3": {"dependencies": {"NJsonSchema": "11.0.0"}, "runtime": {"lib/netstandard2.0/NSwag.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "NSwag.Core.Yaml/14.0.3": {"dependencies": {"NJsonSchema.Yaml": "11.0.0", "NSwag.Core": "14.0.3"}, "runtime": {"lib/netstandard2.0/NSwag.Core.Yaml.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "NSwag.Generation/14.0.3": {"dependencies": {"NJsonSchema.NewtonsoftJson": "11.0.0", "NSwag.Core": "14.0.3"}, "runtime": {"lib/netstandard2.0/NSwag.Generation.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "NSwag.Generation.AspNetCore/14.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "NSwag.Generation": "14.0.3"}, "runtime": {"lib/net8.0/NSwag.Generation.AspNetCore.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "RestSharp/112.1.0": {"runtime": {"lib/net8.0/RestSharp.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Serilog/3.1.1": {"runtime": {"lib/net7.0/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.AspNetCore/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "Serilog": "3.1.1", "Serilog.Extensions.Hosting": "8.0.0", "Serilog.Extensions.Logging": "8.0.0", "Serilog.Formatting.Compact": "2.0.0", "Serilog.Settings.Configuration": "8.0.0", "Serilog.Sinks.Console": "5.0.0", "Serilog.Sinks.Debug": "2.0.0", "Serilog.Sinks.File": "5.0.0"}, "runtime": {"lib/net8.0/Serilog.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Hosting/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Serilog": "3.1.1", "Serilog.Extensions.Logging": "8.0.0"}, "runtime": {"lib/net8.0/Serilog.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.Logging": "9.0.5", "Serilog": "3.1.1"}, "runtime": {"lib/net8.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Formatting.Compact/2.0.0": {"dependencies": {"Serilog": "3.1.1"}, "runtime": {"lib/net7.0/Serilog.Formatting.Compact.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Settings.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyModel": "8.0.2", "Serilog": "3.1.1"}, "runtime": {"lib/net8.0/Serilog.Settings.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Console/5.0.0": {"dependencies": {"Serilog": "3.1.1"}, "runtime": {"lib/net7.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Debug/2.0.0": {"dependencies": {"Serilog": "3.1.1"}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.File/5.0.0": {"dependencies": {"Serilog": "3.1.1"}, "runtime": {"lib/net5.0/Serilog.Sinks.File.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore/8.1.1": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "8.1.1", "Swashbuckle.AspNetCore.SwaggerGen": "8.1.1", "Swashbuckle.AspNetCore.SwaggerUI": "8.1.1"}}, "Swashbuckle.AspNetCore.Annotations/8.1.1": {"dependencies": {"Swashbuckle.AspNetCore.SwaggerGen": "8.1.1"}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.1.1274"}}}, "Swashbuckle.AspNetCore.Swagger/8.1.1": {"dependencies": {"Microsoft.OpenApi": "1.6.23"}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.1.1274"}}}, "Swashbuckle.AspNetCore.SwaggerGen/8.1.1": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "8.1.1"}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.1.1274"}}}, "Swashbuckle.AspNetCore.SwaggerUI/8.1.1": {"runtime": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.1.1274"}}}, "System.ClientModel/1.0.0": {"dependencies": {"System.Memory.Data": "1.0.2"}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.24.5302"}}}, "System.CodeDom/4.4.0": {"runtime": {"lib/netstandard2.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.25519.3"}}}, "System.Composition/6.0.0": {"dependencies": {"System.Composition.AttributedModel": "6.0.0", "System.Composition.Convention": "6.0.0", "System.Composition.Hosting": "6.0.0", "System.Composition.Runtime": "6.0.0", "System.Composition.TypedParts": "6.0.0"}}, "System.Composition.AttributedModel/6.0.0": {"runtime": {"lib/net6.0/System.Composition.AttributedModel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.Convention/6.0.0": {"dependencies": {"System.Composition.AttributedModel": "6.0.0"}, "runtime": {"lib/net6.0/System.Composition.Convention.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.Hosting/6.0.0": {"dependencies": {"System.Composition.Runtime": "6.0.0"}, "runtime": {"lib/net6.0/System.Composition.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.Runtime/6.0.0": {"runtime": {"lib/net6.0/System.Composition.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.TypedParts/6.0.0": {"dependencies": {"System.Composition.AttributedModel": "6.0.0", "System.Composition.Hosting": "6.0.0", "System.Composition.Runtime": "6.0.0"}, "runtime": {"lib/net6.0/System.Composition.TypedParts.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Diagnostics.DiagnosticSource/9.0.5": {"runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "System.Drawing.Common/8.0.4": {"dependencies": {"Microsoft.Win32.SystemEvents": "8.0.0"}, "runtime": {"lib/net8.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.424.16911"}}}, "System.Formats.Asn1/9.0.1": {"runtime": {"lib/net8.0/System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "System.IdentityModel.Tokens.Jwt/8.1.2": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.1.2", "Microsoft.IdentityModel.Tokens": "8.1.2"}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.2.51008"}}}, "System.IO.Packaging/4.7.0": {"runtime": {"lib/netstandard2.0/System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.IO.Pipelines/9.0.1": {"runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "System.Memory.Data/1.0.2": {"runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.221.20802"}}}, "System.Security.Cryptography.Pkcs/9.0.1": {"dependencies": {"Microsoft.Bcl.Cryptography": "9.0.1", "System.Formats.Asn1": "9.0.1"}, "runtime": {"lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "System.Security.Cryptography.ProtectedData/4.7.0": {"runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Security.Cryptography.Xml/8.0.1": {"dependencies": {"System.Security.Cryptography.Pkcs": "9.0.1"}, "runtime": {"lib/net8.0/System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}}}, "System.Text.Encoding.CodePages/9.0.1": {"runtime": {"lib/net8.0/System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Text.Encoding.CodePages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "System.Text.Encodings.Web/9.0.1": {"runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}, "runtimeTargets": {"runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "System.Text.Json/9.0.1": {"dependencies": {"System.IO.Pipelines": "9.0.1", "System.Text.Encodings.Web": "9.0.1"}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "YamlDotNet/13.7.1": {"runtime": {"lib/net7.0/YamlDotNet.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Abstraction/1.0.0": {"dependencies": {"Amazon.Extensions.CognitoAuthentication": "2.5.5", "Ardalis.Specification": "8.0.0", "Dapper": "2.1.35", "Domain": "1.0.0"}, "runtime": {"Abstraction.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Application/1.0.0": {"dependencies": {"AWSSDK.S3": "3.7.405.4", "Abstraction": "1.0.0", "DocumentFormat.OpenXml": "2.16.0", "Domain": "1.0.0", "EPPlus": "7.6.0", "FluentValidation": "11.9.0", "FluentValidation.DependencyInjectionExtensions": "11.9.0", "Hangfire": "1.8.15", "Hangfire.Console": "1.4.2", "Hangfire.Console.Extensions": "1.0.5", "Hangfire.Dashboard.Basic.Authentication": "5.0.0", "Hangfire.PostgreSql": "1.20.10", "Mapster": "7.4.0", "Mapster.DependencyInjection": "1.0.1", "MediatR": "12.2.0", "Microsoft.AspNetCore.SignalR.Client.Core": "8.0.10", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.4", "RestSharp": "112.1.0", "Shared": "1.0.0"}, "runtime": {"Application.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Domain/1.0.0": {"dependencies": {"Finbuckle.MultiTenant": "6.13.1", "Finbuckle.MultiTenant.AspNetCore": "6.13.1", "Finbuckle.MultiTenant.EntityFrameworkCore": "6.13.1", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "8.0.10", "Microsoft.Identity.Web": "3.2.2", "Shared": "1.0.0"}, "runtime": {"Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Infrastructure/1.0.0": {"dependencies": {"AWS.Logger.SeriLog": "3.2.0", "Application": "1.0.0", "Ardalis.Specification.EntityFrameworkCore": "8.0.0", "Asp.Versioning.Mvc": "8.0.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "8.0.10", "Microsoft.AspNetCore.Mvc.Versioning": "5.1.0", "Microsoft.EntityFrameworkCore": "9.0.5", "NSwag.AspNetCore": "14.0.3", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.4", "Serilog.AspNetCore": "8.0.1", "Swashbuckle.AspNetCore": "8.1.1", "Swashbuckle.AspNetCore.Annotations": "8.1.1"}, "runtime": {"Infrastructure.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Shared/1.0.0": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"Migrators/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Amazon.Extensions.CognitoAuthentication/2.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-IrXyYiVHsCl58MNVBgzSTArRjhs970Cm/8HE/Kq4r5iPaPhk8V1YJySFZahZPovmc+xCKTQPaeg0XXNuOzJRZg==", "path": "amazon.extensions.cognitoauthentication/2.5.5", "hashPath": "amazon.extensions.cognitoauthentication.2.5.5.nupkg.sha512"}, "Ardalis.Specification/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-1/YP932oZJtaDlm85rWfqvgOjALbk2+VDZaj36sM2TdsJMNkVqMbF5UzY6f7OfzA8eymv3hnCXIgYmEBEiUOhQ==", "path": "ardalis.specification/8.0.0", "hashPath": "ardalis.specification.8.0.0.nupkg.sha512"}, "Ardalis.Specification.EntityFrameworkCore/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fDaCf5/7rm7Ul7yE8YafyWhxCSmWCPd+/4BUxM26Hbnb2PKZJssjAevzx9ZceASJKe4HUndSEKXJ8iz+R3rsIw==", "path": "ardalis.specification.entityframeworkcore/8.0.0", "hashPath": "ardalis.specification.entityframeworkcore.8.0.0.nupkg.sha512"}, "Asp.Versioning.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YkuUcrqi862hw/p8dKCsfOQ6y2mWTfjKHuQoFUA9GOaoBGZsu/FzsoBm6z28WkQIlXCZR1SG7safgHj2WCO/lw==", "path": "asp.versioning.abstractions/8.0.0", "hashPath": "asp.versioning.abstractions.8.0.0.nupkg.sha512"}, "Asp.Versioning.Http/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-pxHp26pXoCCIFUxFZz4UWPuYDFJ3PB0QpTpC4QRoLhu90eE3FBs0zmDeRO6Sb3y6hMnk6efdQHGbyCzd7XQIrA==", "path": "asp.versioning.http/8.0.0", "hashPath": "asp.versioning.http.8.0.0.nupkg.sha512"}, "Asp.Versioning.Mvc/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zxuA7J6HewdjFF/9Cfb6VWovBTj3MdmLg6PztInratlXGpJ+BZjQzoT8FEMOurzmCxbEFPlQmeMW7b1iJKfsdg==", "path": "asp.versioning.mvc/8.0.0", "hashPath": "asp.versioning.mvc.8.0.0.nupkg.sha512"}, "AWS.Logger.Core/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-ruKBNASE/IBnVZhyeUy0ueuxq1CuMtDHNpU2cqGUg12SxvClkqqY/PVXyM6I4ltsMh/1tF8WdpTBj9k3Fw/PgA==", "path": "aws.logger.core/3.1.0", "hashPath": "aws.logger.core.3.1.0.nupkg.sha512"}, "AWS.Logger.SeriLog/3.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-zslsNobQR3j2o+NrlcuVmrWLNyoyeZKIs5n0tO4+Hz6Z2Wcr9FqqgMxzXpKzkYBzQzfYCB/SlovOGvuWEqnnew==", "path": "aws.logger.serilog/3.2.0", "hashPath": "aws.logger.serilog.3.2.0.nupkg.sha512"}, "AWSSDK.CloudWatchLogs/3.7.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-E9mEMaCVStnkzrs2gb35AbMz8xEGCqMGSnk+yyFJbkmdu9a+w5MVsuwRMkHbqiKn1bs5FTpdQJlV/NtgSYeHbA==", "path": "awssdk.cloudwatchlogs/3.7.0.5", "hashPath": "awssdk.cloudwatchlogs.3.7.0.5.nupkg.sha512"}, "AWSSDK.CognitoIdentity/3.7.300.74": {"type": "package", "serviceable": true, "sha512": "sha512-2WjZrVJn7nm6rWZyDEXVgdyZVdTze4pfZjxdr0Q9gEi+7fCq40z//YQLAlwD44UlzVWpONdLvjcCuA39T5Q/QA==", "path": "awssdk.cognitoidentity/3.7.300.74", "hashPath": "awssdk.cognitoidentity.3.7.300.74.nupkg.sha512"}, "AWSSDK.CognitoIdentityProvider/3.7.303.19": {"type": "package", "serviceable": true, "sha512": "sha512-sw+JuC+IWlXkcXCOpU+dCiLTghhwat+AJ0sl5CHKaZaR71ZDq5iBsYIN6DgInUHxJuJaf7hhvm+Ot9TfpWTJGA==", "path": "awssdk.cognitoidentityprovider/3.7.303.19", "hashPath": "awssdk.cognitoidentityprovider.3.7.303.19.nupkg.sha512"}, "AWSSDK.Core/3.7.400.40": {"type": "package", "serviceable": true, "sha512": "sha512-Bt3id437I3k363mHCvBdpUwdw9P2zM3te+wCZ2EWv+PIOyz1NbYqTfrn04U3x333wxsOfT9Ln+cUJeUw96HsTw==", "path": "awssdk.core/3.7.400.40", "hashPath": "awssdk.core.3.7.400.40.nupkg.sha512"}, "AWSSDK.S3/3.7.405.4": {"type": "package", "serviceable": true, "sha512": "sha512-H+BTXyHB5nxS2B3jv/Ur350TLWx5kRk3S1+Tldtr90kB6/FgjvOp4eFgjl6L51f9yMyn6GuODtukt1vJfKJR8w==", "path": "awssdk.s3/3.7.405.4", "hashPath": "awssdk.s3.3.7.405.4.nupkg.sha512"}, "AWSSDK.SecurityToken/3.7.300.75": {"type": "package", "serviceable": true, "sha512": "sha512-Tr1rbOf9DrZTr3XWPDDnwBjigjKf/QLRIB2V4XGEqaBDYUHnRBuckG2sMNpsq/q9tHSvYvvFXCfIH7R0EpNJBg==", "path": "awssdk.securitytoken/3.7.300.75", "hashPath": "awssdk.securitytoken.3.7.300.75.nupkg.sha512"}, "Azure.Core/1.38.0": {"type": "package", "serviceable": true, "sha512": "sha512-IuEgCoVA0ef7E4pQtpC3+TkPbzaoQfa77HlfJDmfuaJUCVJmn7fT0izamZiryW5sYUFKizsftIxMkXKbgIcPMQ==", "path": "azure.core/1.38.0", "hashPath": "azure.core.1.38.0.nupkg.sha512"}, "Azure.Identity/1.11.4": {"type": "package", "serviceable": true, "sha512": "sha512-Sf4BoE6Q3jTgFkgBkx7qztYOFELBCo+wQgpYDwal/qJ1unBH73ywPztIJKXBXORRzAeNijsuxhk94h0TIMvfYg==", "path": "azure.identity/1.11.4", "hashPath": "azure.identity.1.11.4.nupkg.sha512"}, "Azure.Security.KeyVault.Certificates/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-e2ATU/n2ZDL/S8A8EdrcfKEvKc2BojCrrSpmM+JKnrSTQS32x/W0Ldu8utk+epLKwXvSJRSWtlgdo7X8hG1mCg==", "path": "azure.security.keyvault.certificates/4.6.0", "hashPath": "azure.security.keyvault.certificates.4.6.0.nupkg.sha512"}, "Azure.Security.KeyVault.Secrets/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-vwPceoznuT6glvirZcXlaCQrh1uzTSxpZUi2hRFNumHiS3hVyqIXI5fgWiLtlBzwqPJMTr0flUoSvGKjXXQlfg==", "path": "azure.security.keyvault.secrets/4.6.0", "hashPath": "azure.security.keyvault.secrets.4.6.0.nupkg.sha512"}, "Dapper/2.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-YKRwjVfrG7GYOovlGyQoMvr1/IJdn+7QzNXJxyMh0YfFF5yvDmTYaJOVYWsckreNjGsGSEtrMTpnzxTUq/tZQw==", "path": "dapper/2.1.35", "hashPath": "dapper.2.1.35.nupkg.sha512"}, "DocumentFormat.OpenXml/2.16.0": {"type": "package", "serviceable": true, "sha512": "sha512-RhpnDgyyx1KjZm98T3w3bSXFHG/7ZNUaVmz4NAUA+jmV3PcVNZeW87Y04CpBNLdDHEMSspirfo0B5kLRaoE97w==", "path": "documentformat.openxml/2.16.0", "hashPath": "documentformat.openxml.2.16.0.nupkg.sha512"}, "EPPlus/7.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-ve22wl/mlqjDsiewxXk4edACuIATsDZPFVOyg9l2xSnMLqN6nJHJ0q0MaaDDJwVrnfyTdA9DTJ1MF449gXi+Nw==", "path": "epplus/7.6.0", "hashPath": "epplus.7.6.0.nupkg.sha512"}, "EPPlus.Interfaces/7.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-mGLKgdIKkXRYIu+HIGmZUngVAAlPzIQgI/KqG10m6P5P2112l6p/5dDa35UHu4GV4Qevw0Pq9PxAymrrrl4tzA==", "path": "epplus.interfaces/7.5.0", "hashPath": "epplus.interfaces.7.5.0.nupkg.sha512"}, "EPPlus.System.Drawing/7.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-cgwstM12foFisisURUyxwJOWHMD/rZxPSyBXFsCOFayaKq0oKlOs1mCTueKNNIlpPDG1no9vcaQiJgZXFM4KPA==", "path": "epplus.system.drawing/7.5.0", "hashPath": "epplus.system.drawing.7.5.0.nupkg.sha512"}, "Finbuckle.MultiTenant/6.13.1": {"type": "package", "serviceable": true, "sha512": "sha512-KCO5j2Ehl+R9ZoiC5LSS0jUCLhmGKUCmIaBAB8pymUPhFsTVO1jBDCTD8DXvP177yZUm1v/iJ28/uIoyBUesdQ==", "path": "finbuckle.multitenant/6.13.1", "hashPath": "finbuckle.multitenant.6.13.1.nupkg.sha512"}, "Finbuckle.MultiTenant.AspNetCore/6.13.1": {"type": "package", "serviceable": true, "sha512": "sha512-WLJmfHEQOVToAHOYwzbfW/XXGCna0wTaOyTqLvsiFFTnajXakjjgB8ytZYz0EcLScbP/05IFNzoxp/q8LE+XhQ==", "path": "finbuckle.multitenant.aspnetcore/6.13.1", "hashPath": "finbuckle.multitenant.aspnetcore.6.13.1.nupkg.sha512"}, "Finbuckle.MultiTenant.EntityFrameworkCore/6.13.1": {"type": "package", "serviceable": true, "sha512": "sha512-YjmTR4Mq3DKE2gzM6CYeCtXujYufuTONuc3GkVccFsIHZyDPmufbOfEtOJUKGzXjmRbIVC3vTn/SwekZOxuiWg==", "path": "finbuckle.multitenant.entityframeworkcore/6.13.1", "hashPath": "finbuckle.multitenant.entityframeworkcore.6.13.1.nupkg.sha512"}, "FluentValidation/11.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-VneVlTvwYDkfHV5av3QrQ0amALgrLX6LV94wlYyEsh0B/klJBW7C8y2eAtj5tOZ3jH6CAVpr4s1ZGgew/QWyig==", "path": "fluentvalidation/11.9.0", "hashPath": "fluentvalidation.11.9.0.nupkg.sha512"}, "FluentValidation.DependencyInjectionExtensions/11.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON>++xvN7HUf4WlHJL6bhsybUj/uho8ApOYIdxGjpF8Ot7Fukz6LRfRJ06H0KXhWqmMHWEbu89hJbjKJHtg7b9g==", "path": "fluentvalidation.dependencyinjectionextensions/11.9.0", "hashPath": "fluentvalidation.dependencyinjectionextensions.11.9.0.nupkg.sha512"}, "Hangfire/1.8.15": {"type": "package", "serviceable": true, "sha512": "sha512-yUu3JuoBDsU3Bbs8NTs1XapbGUiLzC8WJUm5z8rPoMyEWxgZRPlWvx+I85mFddQutveieoKKxAdkmgi+M89htA==", "path": "hangfire/1.8.15", "hashPath": "hangfire.1.8.15.nupkg.sha512"}, "Hangfire.AspNetCore/1.8.15": {"type": "package", "serviceable": true, "sha512": "sha512-o85rtOYvhbWpNUGT4YrZE62lugShfdL3EMCqX2QoTC6eXVwpqtWOYfSeiTXAxlbF0CXNJJsuSCISKLmzbngWAA==", "path": "hangfire.aspnetcore/1.8.15", "hashPath": "hangfire.aspnetcore.1.8.15.nupkg.sha512"}, "Hangfire.Console/1.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-N1KeWrvVsJt2jnA6hA9Kid/6CnUMZdC0fVmDpyFAnTAdXrFdpTwvhGwfzoiUWD8boZweA3BvK6CFoZoJhjQTEg==", "path": "hangfire.console/1.4.2", "hashPath": "hangfire.console.1.4.2.nupkg.sha512"}, "Hangfire.Console.Extensions/1.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-5PpMP86s1HJLSJr1tRu8y8NEZGdAZmiaICGdVIlJM/jfAVcTbc0pT3osc7MT0S/ZRoUlwk1ErVWgxARNhtP9aQ==", "path": "hangfire.console.extensions/1.0.5", "hashPath": "hangfire.console.extensions.1.0.5.nupkg.sha512"}, "Hangfire.Core/1.8.15": {"type": "package", "serviceable": true, "sha512": "sha512-+w8gT6CFH4jicVEsJ8WlMRJMNV2MG52JNtvKoXPFHFs6nkDTND6iDeCjydyHgp+85lZPRXc+s9/vkxD2vbPrLg==", "path": "hangfire.core/1.8.15", "hashPath": "hangfire.core.1.8.15.nupkg.sha512"}, "Hangfire.Dashboard.Basic.Authentication/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yB+3BnV4i15mDBxinpCzUAO15Ks6wme8tGotDb1cnxrcd1EDGtQ4MFkidXMVolAiMgc3uL+nYUu6kIMwl9u5Bg==", "path": "hangfire.dashboard.basic.authentication/5.0.0", "hashPath": "hangfire.dashboard.basic.authentication.5.0.0.nupkg.sha512"}, "Hangfire.NetCore/1.8.15": {"type": "package", "serviceable": true, "sha512": "sha512-HNACpklY1FGcsCr/xlPvmh5R5JqH2eEBxOp63Dwph6H6LdGWWqHoMpxjxkpYkZXM2mNpmk+j0Dk8lizadfnD+A==", "path": "hangfire.netcore/1.8.15", "hashPath": "hangfire.netcore.1.8.15.nupkg.sha512"}, "Hangfire.PostgreSql/1.20.10": {"type": "package", "serviceable": true, "sha512": "sha512-Nn/88KoBvmy/xyopC9s+lXkwxQ6VB+RKyM8tjX3EgfSARDFxl2sEsFu0lw7WrjFdosg+E3naGzM5MzyiiL5i6w==", "path": "hangfire.postgresql/1.20.10", "hashPath": "hangfire.postgresql.1.20.10.nupkg.sha512"}, "Hangfire.SqlServer/1.8.15": {"type": "package", "serviceable": true, "sha512": "sha512-bNN0jpw+RdUlFK5UaXz+VhfSIV1Os264XDettv9t6nY9hP2afDzRXWYAuFNamKH6s+oFThGCZXbULZTDAPjASQ==", "path": "hangfire.sqlserver/1.8.15", "hashPath": "hangfire.sqlserver.1.8.15.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "Mapster/7.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-RYGoDqvS4WTKIq0HDyPBBVIj6N0mluOCXQ1Vk95JKseMHEsbCXSEGKSlP95oL+s42IXAXbqvHj7p0YaRBUcfqg==", "path": "mapster/7.4.0", "hashPath": "mapster.7.4.0.nupkg.sha512"}, "Mapster.Core/1.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-11lokmfliBEMMmjeqxFGNpqGXq6tN96zFqpBmfYeahr4Ybk63oDmeJmOflsATjobYkX248g5Y62oQ2NNnZaeww==", "path": "mapster.core/1.2.1", "hashPath": "mapster.core.1.2.1.nupkg.sha512"}, "Mapster.DependencyInjection/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-LfjnRIwx6WAo3ssq8PFeaHFaUz00BfSG9BhWgXsiDa3H5lDhG0lpMGDF6w2ZnooS4eHYmAv4f77VxmzpvgorNg==", "path": "mapster.dependencyinjection/1.0.1", "hashPath": "mapster.dependencyinjection.1.0.1.nupkg.sha512"}, "MediatR/12.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-8TUFrHapKi6D74PhnSNEguRsH91HNGyP3R4ZQdgDorJgl9Wac5Prh0vA33QfrniAaS6L2xNNhc6vxzg+5AIbwA==", "path": "mediatr/12.2.0", "hashPath": "mediatr.12.2.0.nupkg.sha512"}, "MediatR.Contracts/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-FYv95bNT4UwcNA+G/J1oX5OpRiSUxteXaUt2BJbRSdRNiIUNbggJF69wy6mnk2wYToaanpdXZdCwVylt96MpwQ==", "path": "mediatr.contracts/2.0.1", "hashPath": "mediatr.contracts.2.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-rcPXghZCc82IB9U2Px1Ln5Zn3vjV4p83H/Few5T/904hBddjSz03COQ2zOGWBBvdTBY+GciAUJwgBFNWaxLfqw==", "path": "microsoft.aspnetcore.authentication.jwtbearer/8.0.10", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.8.0.10.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.OpenIdConnect/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-gx344DYqTjhotuG56JchlqArEvytZGgYuIuaNP/B+FcvXMuexO3mujamvs4qWXH+5g96poi2+p/Cek5KpVmaQA==", "path": "microsoft.aspnetcore.authentication.openidconnect/8.0.1", "hashPath": "microsoft.aspnetcore.authentication.openidconnect.8.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Connections.Abstractions/8.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-7fqSpSGzBRldyO1obKMPGYUdU4iwvMPsUEgdufgRZe2tBF9rKf8ffGmuObP81N4AKb5A438+5ER5t3ES0r1yDQ==", "path": "microsoft.aspnetcore.connections.abstractions/8.0.10", "hashPath": "microsoft.aspnetcore.connections.abstractions.8.0.10.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.Internal/8.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-MT/jvNoiXUB82drzqtqZqyAfxQH2b0kpEyjjMYrSLmqgAvBkMEKJelbqHazEo5Lxtq43uquPgeBtTuSrVog5lQ==", "path": "microsoft.aspnetcore.cryptography.internal/8.0.10", "hashPath": "microsoft.aspnetcore.cryptography.internal.8.0.10.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/8.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-4jd0g3k2R1L1bhhpVmJOp7rAs76V9XLVuhl8J3sTAcl2dKMS78PsKG1HX75U73WEEwrsM4Bui2/N1/Blwgt5iw==", "path": "microsoft.aspnetcore.cryptography.keyderivation/8.0.10", "hashPath": "microsoft.aspnetcore.cryptography.keyderivation.8.0.10.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-DX2Quy+WRLXPGeWNGtOKyPT2vusNfFI15S9M2DIpaeBqZmXpheoQdlvsPzyn7K8uorcnfUW5ML/vSo26FOQ9BA==", "path": "microsoft.aspnetcore.dataprotection/8.0.1", "hashPath": "microsoft.aspnetcore.dataprotection.8.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection.Abstractions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-4n79+eJnSXaqZIx5c6A+Dtl2bIYwcrAujKDfnDJnTkJa0n5NH4UBCCDNEyONW11UeBYzZb1G4DTE7YWOFbw+9Q==", "path": "microsoft.aspnetcore.dataprotection.abstractions/8.0.1", "hashPath": "microsoft.aspnetcore.dataprotection.abstractions.8.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Http/2.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-BAibpoItxI5puk7YJbIGj95arZueM8B8M5xT1fXBn3hb3L2G3ucrZcYXv1gXdaroLbntUs8qeV8iuBrpjQsrKw==", "path": "microsoft.aspnetcore.http/2.2.2", "hashPath": "microsoft.aspnetcore.http.2.2.2.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "path": "microsoft.aspnetcore.http.features/2.2.0", "hashPath": "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/8.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-vMeY9F3Sq+AiZlquf84rwHOAQBS8nb8kd1RcuoXKPBhHNGBxMLYnr8/e/FCwu7kb14hH/rqWoEuyO4WXpAO6Rw==", "path": "microsoft.aspnetcore.identity.entityframeworkcore/8.0.10", "hashPath": "microsoft.aspnetcore.identity.entityframeworkcore.8.0.10.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Versioning/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-UX8w9BlCiZpr6Ox4YAve1w0CkI1CAovukGNzKd7v0+5pZc8lzuG5tRovucr1RWIKHs0E/Yx8563CN7KzaB3bpw==", "path": "microsoft.aspnetcore.mvc.versioning/5.1.0", "hashPath": "microsoft.aspnetcore.mvc.versioning.5.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Client.Core/8.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-JvteyIThzPPSxzGM5XDn8eSYAGaH4sr1tztXhH5r080VEK8AhT7OnTIX/B3xBOKA6nwf1J6Ic9/GygekduWRUw==", "path": "microsoft.aspnetcore.signalr.client.core/8.0.10", "hashPath": "microsoft.aspnetcore.signalr.client.core.8.0.10.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Common/8.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-co7C/W6Is0DHB3goFHZbTLw1LSgo6uOhlGx36M1c9b2tPYBRlpqVBjuB8GVMLdZEPG4BCfJXBEmU/dU0Kdik8A==", "path": "microsoft.aspnetcore.signalr.common/8.0.10", "hashPath": "microsoft.aspnetcore.signalr.common.8.0.10.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Protocols.Json/8.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-ZyAboyGomA6cUWeLKiYExOaNKsrJiUvQHtyJ1n3xW/RLViRx0tHzCLzyvVJQJQnAIkqQOchbsdM2iMuGDAWI8w==", "path": "microsoft.aspnetcore.signalr.protocols.json/8.0.10", "hashPath": "microsoft.aspnetcore.signalr.protocols.json.8.0.10.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ErxAAKaDzxXASB/b5uLEkLgUWv1QbeVxyJYEHQwMaxXOeFFVkQxiq8RyfVcifLU7NR0QY0p3acqx4ZpYfhHDg==", "path": "microsoft.aspnetcore.webutilities/2.2.0", "hashPath": "microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512"}, "Microsoft.Bcl.Cryptography/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-U/tCi1nw6aflfp+H6Hzwgt+CIHx0PoKKOTF7wNaxIg3EgfzqV+DtUantwRNifRu0Hm0W3xnxZ0hOVZVoORLAyQ==", "path": "microsoft.bcl.cryptography/9.0.1", "hashPath": "microsoft.bcl.cryptography.9.0.1.nupkg.sha512"}, "Microsoft.Bcl.TimeProvider/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-C7kWHJnMRY7EvJev2S8+yJHZ1y7A4ZlLbA4NE+O23BDIAN5mHeqND1m+SKv1ChRS5YlCDW7yAMUe7lttRsJaAA==", "path": "microsoft.bcl.timeprovider/8.0.1", "hashPath": "microsoft.bcl.timeprovider.8.0.1.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-j/rOZtLMVJjrfLRlAMckJLPW/1rze9MT1yfWqSIbUPGRu1m1P0fuo9PmqapwsmePfGB5PJrudQLvmUOAMF0DqQ==", "path": "microsoft.codeanalysis.analyzers/3.3.3", "hashPath": "microsoft.codeanalysis.analyzers.3.3.3.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-lwAbIZNdnY0SUNoDmZHkVUwLO8UyNnyyh1t/4XsbFxi4Ounb3xszIYZaWhyj5ZjyfcwqwmtMbE7fUTVCqQEIdQ==", "path": "microsoft.codeanalysis.common/4.5.0", "hashPath": "microsoft.codeanalysis.common.4.5.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-cM59oMKAOxvdv76bdmaKPy5hfj+oR+zxikWoueEB7CwTko7mt9sVKZI8Qxlov0C/LuKEG+WQwifepqL3vuTiBQ==", "path": "microsoft.codeanalysis.csharp/4.5.0", "hashPath": "microsoft.codeanalysis.csharp.4.5.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-h74wTpmGOp4yS4hj+EvNzEiPgg/KVs2wmSfTZ81upJZOtPkJsVkgfsgtxxqmAeapjT/vLKfmYV0bS8n5MNVP+g==", "path": "microsoft.codeanalysis.csharp.workspaces/4.5.0", "hashPath": "microsoft.codeanalysis.csharp.workspaces.4.5.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.Common/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-l4dDRmGELXG72XZaonnOeORyD/T5RpEu5LGHOUIhnv+MmUWDY/m1kWXGwtcgQ5CJ5ynkFiRnIYzTKXYjUs7rbw==", "path": "microsoft.codeanalysis.workspaces.common/4.5.0", "hashPath": "microsoft.codeanalysis.workspaces.common.4.5.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-TeCtb/vc+jxvgkVAqeJlZKOoG5w/w8AigWQQyOmeJsJ7+0SkONX8bqEV/wB+ojnT0sXuJrrfXQOEC3ws6asEng==", "path": "microsoft.entityframeworkcore/9.0.5", "hashPath": "microsoft.entityframeworkcore.9.0.5.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-81fGyIibhGc4rq4ZxmVZE/1CFSvGMQOZqdRyCBLKz/Hb8eE973dmSfcdXpXhQ/5f+nbax4VGkWhwPGxWUNWaCQ==", "path": "microsoft.entityframeworkcore.abstractions/9.0.5", "hashPath": "microsoft.entityframeworkcore.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-kWRrD69qCXo7lahPZPt7C127UfK0I024laFZEDMfT3JbALB1EWneFvq1utWM0cNKPFuYis1E1oaYTuRGI/9inQ==", "path": "microsoft.entityframeworkcore.analyzers/9.0.5", "hashPath": "microsoft.entityframeworkcore.analyzers.9.0.5.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/8.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-uGNjfKvAsql2KHRqxlK5wHo8mMC60G/FecrFKEjJgeIxtUAbSXGOgKGw/gD9flO5Fzzt1C7uxfIcr6ZsMmFkeg==", "path": "microsoft.entityframeworkcore.design/8.0.10", "hashPath": "microsoft.entityframeworkcore.design.8.0.10.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-7Iu0h4oevRvH4IwPzmxuIJGYRt55TapoREGlluk75KCO7lenN0+QnzCl6cQDY48uDoxAUpJbpK2xW7o8Ix69dw==", "path": "microsoft.entityframeworkcore.relational/9.0.1", "hashPath": "microsoft.entityframeworkcore.relational.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "path": "microsoft.extensions.apidescription.server/6.0.5", "hashPath": "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-RV6wOTvH5BeVRs6cvxFuaV1ut05Dklpvq19XRO1JxAayfLWYIEP7K94aamY0iSUhoehWk1X5H6gMcbZkHuBjew==", "path": "microsoft.extensions.caching.abstractions/9.0.5", "hashPath": "microsoft.extensions.caching.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-qDmoAzIUBup5KZG1Abv51ifbHMCWFnaXbt05l+Sd92mLOpF9OwHOuoxu3XhzXaPGfq0Ns3pv1df5l8zuKjFgGw==", "path": "microsoft.extensions.caching.memory/9.0.5", "hashPath": "microsoft.extensions.caching.memory.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-VuthqFS+ju6vT8W4wevdhEFiRi1trvQtkzWLonApfF5USVzzDcTBoY3F24WvN/tffLSrycArVfX1bThm/9xY2A==", "path": "microsoft.extensions.configuration/9.0.1", "hashPath": "microsoft.extensions.configuration.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+4hfFIY1UjBCXFTTOd+ojlDPq6mep3h5Vq5SYE3Pjucr7dNXmq4S/6P/LoVnZFz2e/5gWp/om4svUFgznfULcA==", "path": "microsoft.extensions.configuration.abstractions/9.0.1", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-mBMoXLsr5s1y2zOHWmKsE9veDcx8h1x/c3rz4baEdQKTeDcmQAPNbB54Pi/lhFO3K431eEq6PFbMgLaa6PHFfA==", "path": "microsoft.extensions.configuration.binder/8.0.0", "hashPath": "microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-QBOI8YVAyKqeshYOyxSe6co22oag431vxMu5xQe1EjXMkYE4xK4J71xLCW3/bWKmr9Aoy1VqGUARSLFnotk4Bg==", "path": "microsoft.extensions.configuration.fileextensions/9.0.1", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-z+g+lgPET1JRDjsOkFe51rkkNcnJgvOK5UIpeTfF1iAi0GkBJz5/yUuTa8a9V8HUh4gj4xFT5WGoMoXoSDKfGg==", "path": "microsoft.extensions.configuration.json/9.0.1", "hashPath": "microsoft.extensions.configuration.json.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-N1Mn0T/tUBPoLL+Fzsp+VCEtneUhhxc1//Dx3BeuQ8AX+XrMlYCfnp2zgpEXnTCB7053CLdiqVWPZ7mEX6MPjg==", "path": "microsoft.extensions.dependencyinjection/9.0.5", "hashPath": "microsoft.extensions.dependencyinjection.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-cjnRtsEAzU73aN6W7vkWy8Phj5t3Xm78HSqgrbh/O4Q9SK/yN73wZVa21QQY6amSLQRQ/M8N+koGnY6PuvKQsw==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.5", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-mUBDZZRgZrSyFOsJ2qJJ9fXfqd/kXJwf3AiDoqLD9m6TjY5OO/vLNOb9fb4juC0487eq4hcGN/M2Rh/CKS7QYw==", "path": "microsoft.extensions.dependencymodel/8.0.2", "hashPath": "microsoft.extensions.dependencymodel.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3PZp/YSkIXrF7QK7PfC1bkyRYwqOHpWFad8Qx+4wkuumAeXo1NHaxpS9LboNA9OvNSAu+QOVlXbMyoY+pHSqcw==", "path": "microsoft.extensions.diagnostics/8.0.0", "hashPath": "microsoft.extensions.diagnostics.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "path": "microsoft.extensions.diagnostics.abstractions/8.0.0", "hashPath": "microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Features/8.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-6SpN2/BuqUnhrw1i+vXsw7CA4ADYt7lf1G9/eDs+bY7eJoug5YQVFd4OS+37m8dSbklCdq6b7rLbCVQUZgL6oQ==", "path": "microsoft.extensions.features/8.0.10", "hashPath": "microsoft.extensions.features.8.0.10.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-DguZYt1DWL05+8QKWL3b6bW7A2pC5kYFMY5iXM6W2M23jhvcNa8v6AU8PvVJBcysxHwr9/jax0agnwoBumsSwg==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.1", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Embedded/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-TuRh62KcoOvaSDCbtHT8K0WYptZysYQHPRRNfOgqF7ZUtUL4O0WMV8RdxbtDFJDsg3jv9bgHwXbrgwTeI9+5uQ==", "path": "microsoft.extensions.fileproviders.embedded/8.0.0", "hashPath": "microsoft.extensions.fileproviders.embedded.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-TKDMNRS66UTMEVT38/tU9hA63UTMvzI3DyNm5mx8+JCf3BaOtxgrvWLCI1y3J52PzT5yNl/T2KN5Z0KbApLZcg==", "path": "microsoft.extensions.fileproviders.physical/9.0.1", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Mxcp9NXuQMvAnudRZcgIb5SqlWrlullQzntBLTwuv0MPIJ5LqiGwbRqiyxgdk+vtCoUkplb0oXy5kAw1t469Ug==", "path": "microsoft.extensions.filesystemglobbing/9.0.1", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AG7HWwVRdCHlaA++1oKDxLsXIBxmDpMPb3VoyOoAghEWnkUvEAdYQUwnV4jJbAaa/nMYNiEh5ByoLauZBEiovg==", "path": "microsoft.extensions.hosting.abstractions/8.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cWz4caHwvx0emoYe7NkHPxII/KkTI8R/LC9qdqJqnKv2poTJ4e2qqPGQqvRoQ5kaSA4FU5IV3qFAuLuOhoqULQ==", "path": "microsoft.extensions.http/8.0.0", "hashPath": "microsoft.extensions.http.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Identity.Core/8.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-tS0lNRccAxuAeIVxLBDdklSOL2vAzVUcYqY0njsRbJpNYrXNIKVeQGmhPJgBU0Vrq+iu0LLJ4KLCqGxsOIWpyw==", "path": "microsoft.extensions.identity.core/8.0.10", "hashPath": "microsoft.extensions.identity.core.8.0.10.nupkg.sha512"}, "Microsoft.Extensions.Identity.Stores/8.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-Mwxhj2pLwFcT8BOJ4g7y/WQyQSmZNOalIHmyISFlWykPEKgaQXOlddOCOftSIUqh4IZEYDsVXjeecjl9RLC8Lw==", "path": "microsoft.extensions.identity.stores/8.0.10", "hashPath": "microsoft.extensions.identity.stores.8.0.10.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-rQU61lrgvpE/UgcAd4E56HPxUIkX/VUQCxWmwDTLLVeuwRDYTL0q/FLGfAW17cGTKyCh7ywYAEnY3sTEvURsfg==", "path": "microsoft.extensions.logging/9.0.5", "hashPath": "microsoft.extensions.logging.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-pP1PADCrIxMYJXxFmTVbAgEU7GVpjK5i0/tyfU9DiE0oXQy3JWQaOVgCkrCiePLgS8b5sghM3Fau3EeHiVWbCg==", "path": "microsoft.extensions.logging.abstractions/9.0.5", "hashPath": "microsoft.extensions.logging.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N3/d0HeMRnBekadbZlmbp+In8EvNNkQHSdbtRzjrGVckdZWpYs5GNrAfaYqVplDFW0WUedSaFJ3khB50BWYGsw==", "path": "microsoft.extensions.logging.configuration/5.0.0", "hashPath": "microsoft.extensions.logging.configuration.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gA8H7uQOnM5gb+L0uTNjViHYr+hRDqCdfugheGo/MxQnuHzmhhzCBTIPm19qL1z1Xe0NEMabfcOBGv9QghlZ8g==", "path": "microsoft.extensions.objectpool/2.2.0", "hashPath": "microsoft.extensions.objectpool.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-vPdJQU8YLOUSSK8NL0RmwcXJr2E0w8xH559PGQl4JYsglgilZr9LZnqV2zdgk+XR05+kuvhBEZKoDVd46o7NqA==", "path": "microsoft.extensions.options/9.0.5", "hashPath": "microsoft.extensions.options.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "path": "microsoft.extensions.options.configurationextensions/8.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-b4OAv1qE1C9aM+ShWJu3rlo/WjDwa/I30aIPXqDWSKXTtKl1Wwh6BZn+glH5HndGVVn3C6ZAPQj5nv7/7HJNBQ==", "path": "microsoft.extensions.primitives/9.0.5", "hashPath": "microsoft.extensions.primitives.9.0.5.nupkg.sha512"}, "Microsoft.Identity.Abstractions/7.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-/lycPG0hdzmlD1MEpkzYQz5uoveO0pTDc32wKlVQ1c0GB1kLqxA7EV5XsSQp6VPTn/7KOa0crwidp15VLZs3eQ==", "path": "microsoft.identity.abstractions/7.1.0", "hashPath": "microsoft.identity.abstractions.7.1.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.65.0": {"type": "package", "serviceable": true, "sha512": "sha512-RV35ZcJ5/P7n+Zu6J3wmtiDdK6MW5h6EpZ0ojjB9sMwNhGHEJCv829cb3kZ4PZ664llYFv8sbUITWWGvBTqv0Q==", "path": "microsoft.identity.client/4.65.0", "hashPath": "microsoft.identity.client.4.65.0.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"type": "package", "serviceable": true, "sha512": "sha512-PWnJcznrSGr25MN8ajlc2XIDW4zCFu0U6FkpaNLEWLgd1NgFCp5uDY3mqLDgM8zCN8hqj8yo5wHYfLB2HjcdGw==", "path": "microsoft.identity.client.extensions.msal/4.61.3", "hashPath": "microsoft.identity.client.extensions.msal.4.61.3.nupkg.sha512"}, "Microsoft.Identity.Web/3.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-N75Gyb0RAxydepwVEhKTDS4Db/GO2pYsSeMvqmQizIKdmQQmCFYMeHdtSTiEsv/bng9zmNOmwarz/d7ONnTMuQ==", "path": "microsoft.identity.web/3.2.2", "hashPath": "microsoft.identity.web.3.2.2.nupkg.sha512"}, "Microsoft.Identity.Web.Certificate/3.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-Xe1xAvGx9ZLy0wLEsmbsSCpIVjWzhsUAl5EN7RWqd+zeb2fEOqRInCxgS9eLCAD4wfrcATzHt3ELHtB5to/b8w==", "path": "microsoft.identity.web.certificate/3.2.2", "hashPath": "microsoft.identity.web.certificate.3.2.2.nupkg.sha512"}, "Microsoft.Identity.Web.Certificateless/3.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-U/liNxeAitSQ/zmSf9W93PdWvXxIVqsrPF0qGY80dsRQy8yu7SxB2X1a7lgzTOqn2BtczemGLyj1QdP/86wNAQ==", "path": "microsoft.identity.web.certificateless/3.2.2", "hashPath": "microsoft.identity.web.certificateless.3.2.2.nupkg.sha512"}, "Microsoft.Identity.Web.Diagnostics/3.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-ms4/yJzRuZ7Qp0lH73tRWz2XnjHopHxrZv0VgJXrqB1HvTP1uCnoq/DM+qs8XVwG2lvejMFplsv5vU3l2xqOOg==", "path": "microsoft.identity.web.diagnostics/3.2.2", "hashPath": "microsoft.identity.web.diagnostics.3.2.2.nupkg.sha512"}, "Microsoft.Identity.Web.TokenAcquisition/3.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-WWqmmG9nE3C+yzduvh8ieb7Hti26ltfGKy9BtzpMoqLTc/VKbHxFl3d9elRztmNeukjUvOmKvtqbykFkeTtCqQ==", "path": "microsoft.identity.web.tokenacquisition/3.2.2", "hashPath": "microsoft.identity.web.tokenacquisition.3.2.2.nupkg.sha512"}, "Microsoft.Identity.Web.TokenCache/3.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-IdblF2ySKDuv12hP/WT1LPovuVOqYepV6UYzABDaJQaFnA8C4fFAV+ez8yp7rT7Mf3iDtRO0W66ESSnlb0NdQQ==", "path": "microsoft.identity.web.tokencache/3.2.2", "hashPath": "microsoft.identity.web.tokencache.3.2.2.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-QSSDer3kvyTdNq6BefgX4EYi1lsia2zJUh5CfIMZFQUh6BhrXK1WE4i2C9ltUmmuUjoeVVX6AaSo9NZfpTGNdw==", "path": "microsoft.identitymodel.abstractions/8.1.2", "hashPath": "microsoft.identitymodel.abstractions.8.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-AWQINMvtamdYBqtG8q8muyYTfA9i5xRBEsMKQdzOn5xRzhVVDSzsNGYof1docfF3pX4hNRUpHlzs61RP0reZMw==", "path": "microsoft.identitymodel.jsonwebtokens/8.1.2", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-pEn//qKJcEXDsLHLzACFrT3a2kkpIGOXLEYkcuxjqWoeDnbeotu0LY9fF8+Ds9WWpVE9ZGlxXamT0VR8rxaQeA==", "path": "microsoft.identitymodel.logging/8.1.2", "hashPath": "microsoft.identitymodel.logging.8.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.LoggingExtensions/8.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-eS/IBBE19VkUL1DtoIhTOO8X0DyYJ1nWEJ9kpxua2tkjnwvn/H3GZBYOoev0B1bB1rcRv+neYEpA6dyhOR9fxQ==", "path": "microsoft.identitymodel.loggingextensions/8.1.2", "hashPath": "microsoft.identitymodel.loggingextensions.8.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/8.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-Yu3UJWIFX2/5m2MZskECqByr62L8A0uTtTblWIxy0wJNUg0OJGhIK6oRdpcZ8xbSJYD/SOE8psjo5IXRqC3Bsw==", "path": "microsoft.identitymodel.protocols/8.1.2", "hashPath": "microsoft.identitymodel.protocols.8.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-eEtnzZiYymJYaguYeIXyviUocltBQzeYI0bEtot1Nrnl+qklCZARgk+SAaeYfdmc9CYo7aqP5UJ78rTTSTpQGQ==", "path": "microsoft.identitymodel.protocols.openidconnect/8.1.2", "hashPath": "microsoft.identitymodel.protocols.openidconnect.8.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-ZSzGsAA3BY20XHnsp8OjrHFtpd+pQtiu4UJDjPtXwCtEzcE5CjWP/8iZEJXy5AxVEFB0z6EwLSN+T1Fsdpjifw==", "path": "microsoft.identitymodel.tokens/8.1.2", "hashPath": "microsoft.identitymodel.tokens.8.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Validators/8.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-gC4VQ1CGBFlPbx8T6kMLSuzs9SPTpK53L+oT+cbIVvwJFPorw/kvgfwvASGN2BN1Rh8naz5wfVXSKm25LpYlKQ==", "path": "microsoft.identitymodel.validators/8.1.2", "hashPath": "microsoft.identitymodel.validators.8.1.2.nupkg.sha512"}, "Microsoft.IO.RecyclableMemoryStream/3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-s/s20YTVY9r9TPfTrN5g8zPF1YhwxyqO6PxUkrYTGI2B+OGPe9AdajWZrLhFqXIvqIW23fnUE4+ztrUWNU1+9g==", "path": "microsoft.io.recyclablememorystream/3.0.1", "hashPath": "microsoft.io.recyclablememorystream.3.0.1.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-iZNkjYqlo8sIOI0bQfpsSoMTmB/kyvmV2h225ihyZT33aTp48ZpF6qYnXxzSXmHt8DpBAwBTX+1s1UFLbYfZKg==", "path": "microsoft.net.http.headers/2.2.0", "hashPath": "microsoft.net.http.headers.2.2.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.OpenApi/1.6.23": {"type": "package", "serviceable": true, "sha512": "sha512-tZ1I0KXnn98CWuV8cpI247A17jaY+ILS9vvF7yhI0uPPEqF4P1d7BWL5Uwtel10w9NucllHB3nTkfYTAcHAh8g==", "path": "microsoft.openapi/1.6.23", "hashPath": "microsoft.openapi.1.6.23.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9opKRyOKMCi2xJ7Bj7kxtZ1r9vbzosMvRrdEhVhDz8j8MoBGgB+WmC94yH839NPH+BclAjtQ/pyagvi/8gDLkw==", "path": "microsoft.win32.systemevents/8.0.0", "hashPath": "microsoft.win32.systemevents.8.0.0.nupkg.sha512"}, "Mono.TextTemplating/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-KZYeKBET/2Z0gY1WlTAK7+RHTl7GSbtvTLDXEZZojUdAPqpQNDL6tHv7VUpqfX5VEOh+uRGKaZXkuD253nEOBQ==", "path": "mono.texttemplating/2.2.1", "hashPath": "mono.texttemplating.2.2.1.nupkg.sha512"}, "Namotion.Reflection/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Qn0wM7u9TpSpja2x8UVexr2bLHb1DGMNhD2TCz3woklxaY1oH+Sitrw9fg/4YbNoNtczeH2jf+yPdXMQlgvFlQ==", "path": "namotion.reflection/3.1.1", "hashPath": "namotion.reflection.3.1.1.nupkg.sha512"}, "NETStandard.Library/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-WcSp3+vP+yHNgS8EV5J7pZ9IRpeDuARBPN28by8zqff1wJQXm26PVU8L3/fYLBJVU7BtDyqNVWq2KlCVvSSR4A==", "path": "netstandard.library/1.6.1", "hashPath": "netstandard.library.1.6.1.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "NJsonSchema/11.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yJviNfW8U8+ACJO0VyiNuNIDGoRDZO5awNfjL1+6iO7TVI5pfjun+ZBVsv1hLga/IVlhnUPpMj8VuhQAYfXD/A==", "path": "njsonschema/11.0.0", "hashPath": "njsonschema.11.0.0.nupkg.sha512"}, "NJsonSchema.Annotations/11.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kbUrZfspa+Y5Kz0OaRbLQxLtVydWFvkY1CpwfKmravZXG2icphuYHR58EwBZuCQWJb/BL81PGP4FjpDNBFnn6Q==", "path": "njsonschema.annotations/11.0.0", "hashPath": "njsonschema.annotations.11.0.0.nupkg.sha512"}, "NJsonSchema.NewtonsoftJson/11.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MCugHG7dyQhfwgY1NIaCZNFQzTYwmQpfwm15bksG/Ng1H8up/4DxxH1M9fDtV5xqYBrWGjMRSmTokGr9wwLCPg==", "path": "njsonschema.newtonsoftjson/11.0.0", "hashPath": "njsonschema.newtonsoftjson.11.0.0.nupkg.sha512"}, "NJsonSchema.Yaml/11.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-riCNrkN2YOVYyvvJjtc7zdIYXQT6/l7FMe0XFGH5rfxWN4/Iy/T60+ZzketAIMhzqn65WPmFZ3NUJ1nGtGCrMw==", "path": "njsonschema.yaml/11.0.0", "hashPath": "njsonschema.yaml.11.0.0.nupkg.sha512"}, "Npgsql/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-tPvY61CxOAWxNsKLEBg+oR646X4Bc8UmyQ/tJszL/7mEmIXQnnBhVJZrZEEUv0Bstu0mEsHZD5At3EO8zQRAYw==", "path": "npgsql/9.0.3", "hashPath": "npgsql.9.0.3.nupkg.sha512"}, "Npgsql.EntityFrameworkCore.PostgreSQL/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-mw5vcY2IEc7L+IeGrxpp/J5OSnCcjkjAgJYCm/eD52wpZze8zsSifdqV7zXslSMmfJG2iIUGZyo3KuDtEFKwMQ==", "path": "npgsql.entityframeworkcore.postgresql/9.0.4", "hashPath": "npgsql.entityframeworkcore.postgresql.9.0.4.nupkg.sha512"}, "NSwag.Annotations/14.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-FOK/EvGfnNcVTRPaZXsngmZJl0uOB51/8M5xSdDYqVLhrqxmSOsL/SIISVecRy/9nbg5dmVsHS20of6papLk5w==", "path": "nswag.annotations/14.0.3", "hashPath": "nswag.annotations.14.0.3.nupkg.sha512"}, "NSwag.AspNetCore/14.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-GFCf/IIEXwwZ+03py6NpQNz0ARkcLdWxKLYX0EYiiAXoTVBij+g5KIl5/j1FoIH79b30twyNOXcEmdIqA/ZWWQ==", "path": "nswag.aspnetcore/14.0.3", "hashPath": "nswag.aspnetcore.14.0.3.nupkg.sha512"}, "NSwag.Core/14.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-lC5NTB2c+/JfQGYOFtVTKy2z8pZD/lsaRGYxzbmJsvNgGfeEq4FiT7yumYEUMvi+lnrv5R58cuqgdLoLdIqqVQ==", "path": "nswag.core/14.0.3", "hashPath": "nswag.core.14.0.3.nupkg.sha512"}, "NSwag.Core.Yaml/14.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-s3PFor6CJ3T2+s5FUh4AaqmPNnsl25rnu01lpqBAy+PIkJjzlMp47xTeBOl0I6bBg5XDcGCiqR/VGjkfRoA/eA==", "path": "nswag.core.yaml/14.0.3", "hashPath": "nswag.core.yaml.14.0.3.nupkg.sha512"}, "NSwag.Generation/14.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-76o7oIymq7oOOMIJLSpMDw9XhP8rgl/RgnFUTY41unRdG8W7Xn1U5VpTmF1I7hI/pgVcp/xwHuTcwgaG1IKnIQ==", "path": "nswag.generation/14.0.3", "hashPath": "nswag.generation.14.0.3.nupkg.sha512"}, "NSwag.Generation.AspNetCore/14.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-mMixV2r4/nl8j+7QtkS/5ig5E+5fLhwlTeoMTfdNVY7J9ggyS/xVZlBrgqtgLt9a9gvkNaZMrLu2jacjOvZcqg==", "path": "nswag.generation.aspnetcore/14.0.3", "hashPath": "nswag.generation.aspnetcore.14.0.3.nupkg.sha512"}, "RestSharp/112.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-bOUGNZqhhv/QeCXHTKEUil846yBjwWXpzPKjO67kFvaAOoF361z7jLY5BMnuZ6WD2WQRA750sNMcK7MSPCQ5Mg==", "path": "restsharp/112.1.0", "hashPath": "restsharp.112.1.0.nupkg.sha512"}, "Serilog/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-P6G4/4Kt9bT635bhuwdXlJ2SCqqn2nhh4gqFqQueCOr9bK/e7W9ll/IoX1Ter948cV2Z/5+5v8pAfJYUISY03A==", "path": "serilog/3.1.1", "hashPath": "serilog.3.1.1.nupkg.sha512"}, "Serilog.AspNetCore/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-B/X+wAfS7yWLVOTD83B+Ip9yl4MkhioaXj90JSoWi1Ayi8XHepEnsBdrkojg08eodCnmOKmShFUN2GgEc6c0CQ==", "path": "serilog.aspnetcore/8.0.1", "hashPath": "serilog.aspnetcore.8.0.1.nupkg.sha512"}, "Serilog.Extensions.Hosting/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-db0OcbWeSCvYQkHWu6n0v40N4kKaTAXNjlM3BKvcbwvNzYphQFcBR+36eQ/7hMMwOkJvAyLC2a9/jNdUL5NjtQ==", "path": "serilog.extensions.hosting/8.0.0", "hashPath": "serilog.extensions.hosting.8.0.0.nupkg.sha512"}, "Serilog.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YEAMWu1UnWgf1c1KP85l1SgXGfiVo0Rz6x08pCiPOIBt2Qe18tcZLvdBUuV5o1QHvrs8FAry9wTIhgBRtjIlEg==", "path": "serilog.extensions.logging/8.0.0", "hashPath": "serilog.extensions.logging.8.0.0.nupkg.sha512"}, "Serilog.Formatting.Compact/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ob6z3ikzFM3D1xalhFuBIK1IOWf+XrQq+H4KeH4VqBcPpNcmUgZlRQ2h3Q7wvthpdZBBoY86qZOI2LCXNaLlNA==", "path": "serilog.formatting.compact/2.0.0", "hashPath": "serilog.formatting.compact.2.0.0.nupkg.sha512"}, "Serilog.Settings.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-nR0iL5HwKj5v6ULo3/zpP8NMcq9E2pxYA6XKTSWCbugVs4YqPyvaqaKOY+OMpPivKp7zMEpax2UKHnDodbRB0Q==", "path": "serilog.settings.configuration/8.0.0", "hashPath": "serilog.settings.configuration.8.0.0.nupkg.sha512"}, "Serilog.Sinks.Console/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IZ6bn79k+3SRXOBpwSOClUHikSkp2toGPCZ0teUkscv4dpDg9E2R2xVsNkLmwddE4OpNVO3N0xiYsAH556vN8Q==", "path": "serilog.sinks.console/5.0.0", "hashPath": "serilog.sinks.console.5.0.0.nupkg.sha512"}, "Serilog.Sinks.Debug/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y6g3OBJ4JzTyyw16fDqtFcQ41qQAydnEvEqmXjhwhgjsnG/FaJ8GUqF5ldsC/bVkK8KYmqrPhDO+tm4dF6xx4A==", "path": "serilog.sinks.debug/2.0.0", "hashPath": "serilog.sinks.debug.2.0.0.nupkg.sha512"}, "Serilog.Sinks.File/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uwV5hdhWPwUH1szhO8PJpFiahqXmzPzJT/sOijH/kFgUx+cyoDTMM8MHD0adw9+Iem6itoibbUXHYslzXsLEAg==", "path": "serilog.sinks.file/5.0.0", "hashPath": "serilog.sinks.file.5.0.0.nupkg.sha512"}, "Swashbuckle.AspNetCore/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-HJHexmU0PiYevgTLvKjYkxEtclF2w4O7iTd3Ef3p6KeT0kcYLpkFVgCw6glpGS57h8769anv8G+NFi9Kge+/yw==", "path": "swashbuckle.aspnetcore/8.1.1", "hashPath": "swashbuckle.aspnetcore.8.1.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.Annotations/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-7HPeJGAs2VikmUOUHjmXo657FhUEuwajjgUmLTVrzGHo4tS1Io29cyMMfMDp4eAnXnY88jMa4MwG00xhWRgIDg==", "path": "swashbuckle.aspnetcore.annotations/8.1.1", "hashPath": "swashbuckle.aspnetcore.annotations.8.1.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-h+8D5jQtnl6X4f2hJQwf0Khj0SnCQANzirCELjXJ6quJ4C1aNNCvJrAsQ+4fOKAMqJkvW48cKj79ftG+YoGcRg==", "path": "swashbuckle.aspnetcore.swagger/8.1.1", "hashPath": "swashbuckle.aspnetcore.swagger.8.1.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-2EuPzXSNleOOzYvziERWRLnk1Oz9i0Z1PimaUFy1SasBqeV/rG+eMfwFAMtTaf4W6gvVOzRcUCNRHvpBIIzr+A==", "path": "swashbuckle.aspnetcore.swaggergen/8.1.1", "hashPath": "swashbuckle.aspnetcore.swaggergen.8.1.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-GDLX/MpK4oa2nYC1N/zN2UidQTtVKLPF6gkdEmGb0RITEwpJG9Gu8olKqPYnKqVeFn44JZoCS0M2LGRKXP8B/A==", "path": "swashbuckle.aspnetcore.swaggerui/8.1.1", "hashPath": "swashbuckle.aspnetcore.swaggerui.8.1.1.nupkg.sha512"}, "System.ClientModel/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-I3CVkvxeqFYjIVEP59DnjbeoGNfo/+SZrCLpRz2v/g0gpCHaEMPtWSY0s9k/7jR1rAsLNg2z2u1JRB76tPjnIw==", "path": "system.clientmodel/1.0.0", "hashPath": "system.clientmodel.1.0.0.nupkg.sha512"}, "System.CodeDom/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-2sCCb7doXEwtYAbqzbF/8UAeDRMNmPaQbU2q50Psg1J9KzumyVVCgKQY8s53WIPTufNT0DpSe9QRvVjOzfDWBA==", "path": "system.codedom/4.4.0", "hashPath": "system.codedom.4.4.0.nupkg.sha512"}, "System.Composition/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-d7wMuKQtfsxUa7S13tITC8n1cQzewuhD5iDjZtK2prwFfKVzdYtgrTHgjaV03Zq7feGQ5gkP85tJJntXwInsJA==", "path": "system.composition/6.0.0", "hashPath": "system.composition.6.0.0.nupkg.sha512"}, "System.Composition.AttributedModel/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WK1nSDLByK/4VoC7fkNiFuTVEiperuCN/Hyn+VN30R+W2ijO1d0Z2Qm0ScEl9xkSn1G2MyapJi8xpf4R8WRa/w==", "path": "system.composition.attributedmodel/6.0.0", "hashPath": "system.composition.attributedmodel.6.0.0.nupkg.sha512"}, "System.Composition.Convention/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-XYi4lPRdu5bM4JVJ3/UIHAiG6V6lWWUlkhB9ab4IOq0FrRsp0F4wTyV4Dj+Ds+efoXJ3qbLqlvaUozDO7OLeXA==", "path": "system.composition.convention/6.0.0", "hashPath": "system.composition.convention.6.0.0.nupkg.sha512"}, "System.Composition.Hosting/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-w/wXjj7kvxuHPLdzZ0PAUt++qJl03t7lENmb2Oev0n3zbxyNULbWBlnd5J5WUMMv15kg5o+/TCZFb6lSwfaUUQ==", "path": "system.composition.hosting/6.0.0", "hashPath": "system.composition.hosting.6.0.0.nupkg.sha512"}, "System.Composition.Runtime/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qkRH/YBaMPTnzxrS5RDk1juvqed4A6HOD/CwRcDGyPpYps1J27waBddiiq1y93jk2ZZ9wuA/kynM+NO0kb3PKg==", "path": "system.composition.runtime/6.0.0", "hashPath": "system.composition.runtime.6.0.0.nupkg.sha512"}, "System.Composition.TypedParts/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-iUR1eHrL8Cwd82neQCJ00MpwNIBs4NZgXzrPqx8NJf/k4+mwBO0XCRmHYJT4OLSwDDqh5nBLJWkz5cROnrGhRA==", "path": "system.composition.typedparts/6.0.0", "hashPath": "system.composition.typedparts.6.0.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-WoI5or8kY2VxFdDmsaRZ5yaYvvb+4MCyy66eXo79Cy1uMa7qXeGIlYmZx7R9Zy5S4xZjmqvkk2V8L6/vDwAAEA==", "path": "system.diagnostics.diagnosticsource/9.0.5", "hashPath": "system.diagnostics.diagnosticsource.9.0.5.nupkg.sha512"}, "System.Drawing.Common/8.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-3G4xpa8mUYGzEF0HlswlBArAFywHJIzsZoB5hU4yMlnYHaabj/lg019BwbyyYBxj0aoM7Cz+jdlgUemeno9LOQ==", "path": "system.drawing.common/8.0.4", "hashPath": "system.drawing.common.8.0.4.nupkg.sha512"}, "System.Formats.Asn1/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-efRn3TXUx2aWG4yOjD5jxTYlPy4Pz/8fiwYBtIpVb/+ySsNA9PFWFd3M3MdcRx1XjpYtj5QSXGm8XnGCeh7dSA==", "path": "system.formats.asn1/9.0.1", "hashPath": "system.formats.asn1.9.0.1.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-UoidlNYjML1ZbV5s8bLP84VpxDzv8uhHzyt5YkZwqLmFTmtOQheNuTKpR/5UWmO5Ka4JT3kVmhUNq5Li733wTg==", "path": "system.identitymodel.tokens.jwt/8.1.2", "hashPath": "system.identitymodel.tokens.jwt.8.1.2.nupkg.sha512"}, "System.IO.Packaging/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-9VV4KAbgRQZ79iEoG40KIeZy38O30oWwewScAST879+oki8g/Wa2HXZQgrhDDxQM4GkP1PnRJll05NMiVPbYAw==", "path": "system.io.packaging/4.7.0", "hashPath": "system.io.packaging.4.7.0.nupkg.sha512"}, "System.IO.Pipelines/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-uXf5o8eV/gtzDQY4lGROLFMWQvcViKcF8o4Q6KpIOjloAQXrnscQSu6gTxYJMHuNJnh7szIF9AzkaEq+zDLoEg==", "path": "system.io.pipelines/9.0.1", "hashPath": "system.io.pipelines.9.0.1.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-782UEbbkZE5tiYYsUQWn4VuzJCvpJRp3umlC9oEELyWZAB1swMZr/KMlJN/0+BSVolYt9OnbOKqWbcoHbdpIeg==", "path": "system.security.cryptography.pkcs/9.0.1", "hashPath": "system.security.cryptography.pkcs.9.0.1.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ehYW0m9ptxpGWvE4zgqongBVWpSDU/JCFD4K7krxkQwSz/sFQjEXCUqpvencjy6DYDbn7Ig09R8GFffu8TtneQ==", "path": "system.security.cryptography.protecteddata/4.7.0", "hashPath": "system.security.cryptography.protecteddata.4.7.0.nupkg.sha512"}, "System.Security.Cryptography.Xml/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-hqu2ztecOf3BYg5q1R7QcyliX9L7r3mfsWtaRitAxcezH8hyZMB7zCmhi186hsUZXk1KxsAHXwyPEW+xvUED6g==", "path": "system.security.cryptography.xml/8.0.1", "hashPath": "system.security.cryptography.xml.8.0.1.nupkg.sha512"}, "System.Text.Encoding.CodePages/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-7Op2OR5RKJgviMpPsXbcCN5bxew/KmW1AM3bzwFo3kShenYjy9eV5lYYuIxWinhrLMooGu6t2ktMRN9toV7l0Q==", "path": "system.text.encoding.codepages/9.0.1", "hashPath": "system.text.encoding.codepages.9.0.1.nupkg.sha512"}, "System.Text.Encodings.Web/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-XkspqduP2t1e1x2vBUAD/xZ5ZDvmywuUwsmB93MvyQLospJfqtX0GsR/kU0vUL2h4kmvf777z3txV2W4NrQ9Qg==", "path": "system.text.encodings.web/9.0.1", "hashPath": "system.text.encodings.web.9.0.1.nupkg.sha512"}, "System.Text.Json/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-eqWHDZqYPv1PvuvoIIx5pF74plL3iEOZOl/0kQP+Y0TEbtgNnM2W6k8h8EPYs+LTJZsXuWa92n5W5sHTWvE3VA==", "path": "system.text.json/9.0.1", "hashPath": "system.text.json.9.0.1.nupkg.sha512"}, "YamlDotNet/13.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-X4m1PnFcJwvAj1sCDMntg/eZcX96CJLrWMiYnq41KqhFVZPuw63ZTSxIGqgdCwHWHvCAyTxheELC/VDf1HsU2A==", "path": "yamldotnet/13.7.1", "hashPath": "yamldotnet.13.7.1.nupkg.sha512"}, "Abstraction/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Application/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Infrastructure/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Shared/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}